---
# Source: connect-cluster-location-platform-staging/templates/kafka-connect-ui.yaml
apiVersion: v1
kind: Service
metadata:
  name: kafka-connect-ui-service
  namespace: kafka-resources
  labels:
    app: kafka-connect-ui
spec:
  selector:
    app: kafka-connect-ui
  ports:
  - port: 8000
    targetPort: 8000
    name: http
  type: ClusterIP
---
# Source: connect-cluster-location-platform-staging/templates/kafka-connect-ui.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-connect-ui
  namespace: kafka-resources
  labels:
    app: kafka-connect-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-connect-ui
  template:
    metadata:
      labels:
        app: kafka-connect-ui
    spec:
      containers:
      - name: kafka-connect-ui
        image: landoop/kafka-connect-ui:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: CONNECT_URL
          value: "http://location-platform-staging-cluster-connect-api:8083"
        - name: PROXY
          value: "true"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
---
# Source: connect-cluster-location-platform-staging/templates/connect-cluster.yaml
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: location-platform-staging-cluster
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  image: docker.io/shqear/debezium:latest
  version: 3.7.0
  replicas: 1
  bootstrapServers: staging.kafka.shared-dev.eu-west-1.careem-tech.com:9094
  authentication:
    type: scram-sha-512
    username: kafka-connect-location-platform-staging
    passwordSecret:
      secretName: kafka-user-kafka-connect-location-platform-staging
      password: password

  config:
    group.id: "location-platform-staging-group"

    # -1 means it will use the default replication factor configured in the broker
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1

    # Topic to use for storing offsets.
    offset.storage.topic: "location-platform-staging-offsets"

    # Topic to use for storing connector and task configurations
    config.storage.topic: "location-platform-staging-configs"

    # Topic to use for storing statuses.
    status.storage.topic: "location-platform-staging-status"
---
# Source: connect-cluster-location-platform-staging/templates/location-platform-connectors.yaml
# Location Platform PostGIS CDC Source Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: location-platform-staging-cluster
  name: location-platform-postgis-source-connector
  namespace: kafka-resources
spec:
  class: io.debezium.connector.postgresql.PostgresConnector
  config:
    connector.class: io.debezium.connector.postgresql.PostgresConnector
    database.hostname: "location-platform-db-prod.cgevunlz3bfz.eu-west-1.rds.amazonaws.com"
    database.port: "5432"
    database.user: "srv_kafka_connect_repl"
    database.password: "careem123"
    database.dbname: "locationPlatformDb"
    database.server.name: "postgres_prod"

    # Logical decoding configuration
    plugin.name: "pgoutput"
    slot.name: "location_platform_debezium_slot"
    publication.name: "location_platform_publication"
    publication.autocreate.mode: "filtered"

    # Table configuration - all public schema tables
    schema.include.list: "public"
    table.include.list: "public.*"

    # Snapshot configuration
    snapshot.mode: "initial"

    # Topic configuration - using existing topic prefix
    topic.prefix: "location_platform_dbz_postgres_prod_"

    # PostGIS-specific configurations
    replica.identity.autoset.values: "public.*:FULL"
    tombstones.on.delete: "false"

    # Handle spatial data types properly
    binary.handling.mode: "base64"

    # SSL configuration for RDS
    database.sslmode: "require"

    # Schema history configuration
    schema.history.internal.kafka.bootstrap.servers: "staging.kafka.shared-dev.eu-west-1.careem-tech.com:9094"
    schema.history.internal.kafka.topic: "location_platform_dbz_postgres_prod_schema-history"
    schema.history.internal.consumer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.consumer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.consumer.sasl.jaas.config: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"kafka-connect-location-platform-staging\" password=\"${file:/opt/kafka/connect-password/password:password}\";"
    schema.history.internal.producer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.producer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.producer.sasl.jaas.config: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"kafka-connect-location-platform-staging\" password=\"${file:/opt/kafka/connect-password/password:password}\";"

  tasksMax: 1
---
# Source: connect-cluster-location-platform-staging/templates/location-platform-connectors.yaml
# Location Platform PostGIS Multi-Table JDBC Sink Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: location-platform-staging-cluster
  name: location-platform-postgis-sink-connector
  namespace: kafka-resources
spec:
  class: io.debezium.connector.jdbc.JdbcSinkConnector
  config:
    connector.class: io.debezium.connector.jdbc.JdbcSinkConnector
    connection.url: "***************************************************************************************************************************"
    connection.username: "srv_kafka_connect_repl"
    connection.password: "careem123"

    # Multi-topic configuration - handles all tables from the Location Platform PostGIS CDC source
    topics.regex: "location_platform_dbz_postgres_prod_\\.public\\..*"

    # Use ByLogicalTableRouter transform for dynamic table name mapping
    transforms: "route"
    transforms.route.type: "io.debezium.transforms.ByLogicalTableRouter"
    transforms.route.topic.regex: "location_platform_dbz_postgres_prod_\\.public\\.(.*)"
    transforms.route.topic.replacement: "$1"
    transforms.route.key.enforce.uniqueness: "false"

    # Connector behavior
    insert.mode: "upsert"
    delete.enabled: "true"
    primary.key.mode: "record_key"
    primary.key.fields: "id"
    schema.evolution: "basic"

    # Performance optimizations
    batch.size: "1000"
    use.reduction.buffer: "true"

    # Connection pool settings
    connection.pool.min_size: "2"
    connection.pool.max_size: "10"
    connection.pool.acquire_increment: "2"

    # PostGIS-specific settings for spatial data handling
    quote.sql.identifiers: "always"

  # Multiple tasks for better parallelism
  tasksMax: 2
---
# Source: connect-cluster-location-platform-staging/templates/kafka-topics.yaml
# Kafka Connect Internal Topics - Configs
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: location-platform-staging-configs
  namespace: kafka-resources
  labels:
    strimzi.io/cluster: kafka-dev-shared-staging
    service_name: location-platform-replication
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 1
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1
---
# Source: connect-cluster-location-platform-staging/templates/kafka-topics.yaml
# Kafka Connect Internal Topics - Offsets
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: location-platform-staging-offsets
  namespace: kafka-resources
  labels:
    strimzi.io/cluster: kafka-dev-shared-staging
    service_name: location-platform-replication
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 25
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1
---
# Source: connect-cluster-location-platform-staging/templates/kafka-topics.yaml
# Kafka Connect Internal Topics - Status
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: location-platform-staging-status
  namespace: kafka-resources
  labels:
    strimzi.io/cluster: kafka-dev-shared-staging
    service_name: location-platform-replication
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 5
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1
---
# Source: connect-cluster-location-platform-staging/templates/kafka-topics.yaml
# Schema History Topic for Location Platform PostGIS Source Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: location_platform_dbz_postgres_prod_schema-history
  namespace: kafka-resources
  labels:
    strimzi.io/cluster: kafka-dev-shared-staging
    service_name: location-platform-replication
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 1
  replicas: 2
  config:
    cleanup.policy: delete
    compression.type: producer
    min.insync.replicas: 1
    retention.ms: *********  # 7 days
---
# Source: connect-cluster-location-platform-staging/templates/user.yaml
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: kafka-connect-location-platform-staging
  namespace: kafka-resources
  labels:
    strimzi.io/cluster: kafka-dev-shared-staging
    service_name: location-platform-replication
  annotations:
    careem.com/aws-secret-account-name: dev-rh
    careem.com/aws-secret-enable: 'false'
    careem.com/aws-secret-name: storage/staging/location-platform/kafka-connect
    careem.com/aws-secret-pass-key-name: kafka_connect_password
    careem.com/aws-secret-user-key-name: kafka_connect_username
    careem.com/force-allow-delete: 'true'
spec:
  authentication:
    type: scram-sha-512
  authorization:
    type: simple
    acls:

      # Allow to list all topics
      - resource:
          type: topic
          name: "*"
          patternType: literal
        operations:
          - Describe

      # Allow to read and write to all connect topics
      - resource:
          type: topic
          name: "location-platform-staging-"
          patternType: prefix
        operations:
          - Read
          - Write
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector to write to topics
      - resource:
          type: topic
          name: "location_platform_dbz_postgres_prod_"
          patternType: prefix
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector heartbeat topic
      - resource:
          type: topic
          name: "__debezium-heartbeat.location_platform_dbz_postgres_prod_"
          patternType: literal
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector schema history topic
      - resource:
          type: topic
          name: "location_platform_dbz_postgres_prod_schema-history"
          patternType: literal
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector consumer groups
      - resource:
          type: group
          name: "location_platform_dbz_postgres_prod_"
          patternType: prefix
        operations:
          - Read

      # Allow Location Platform PostGIS Multi-Topic JDBC Sink Connector to read from topics
      - resource:
          type: topic
          name: "location_platform_dbz_postgres_prod_"
          patternType: prefix
        operations:
          - Read
          - DescribeConfigs

      # Allow Location Platform PostGIS Multi-Topic JDBC Sink Connector consumer group
      - resource:
          type: group
          name: "connect-location-platform-postgis-sink-connector"
          patternType: literal
        operations:
          - Read

      # Allow to access connect group
      - resource:
          type: group
          name: location-platform-staging-group
          patternType: literal
        operations:
          - Read

      - resource:
          type: group
          name: location-platform-staging-group-
          patternType: prefix
        operations:
          - Read
          - Write
        host: "*"
