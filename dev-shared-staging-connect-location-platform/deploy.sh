#!/bin/bash -e

# Save the current working directory
pushd ./chart > /dev/null

# Check if the release exists
if helm -n kafka-resources status location-platform-staging > /dev/null 2>&1; then
  # If the release exists, upgrade it
  helm upgrade -n kafka-resources location-platform-staging ./ -f values.yaml
else
  # If the release does not exist, install it
  helm install -n kafka-resources location-platform-staging ./ -f values.yaml
fi

# Restore the original working directory
popd > /dev/null
