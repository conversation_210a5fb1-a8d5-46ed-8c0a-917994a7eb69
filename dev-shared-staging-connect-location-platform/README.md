# Location Platform PostGIS Replication - Staging Environment

This Helm chart deploys Kafka Connect connectors specifically for replicating PostGIS data from the Location Platform database in the staging environment (`dev-shared-staging`).

## Overview

- **Source Database**: `location-platform-db-prod.cgevunlz3bfz.eu-west-1.rds.amazonaws.com`
- **Target Database**: `location-platform-db-enc.cgevunlz3bfz.eu-west-1.rds.amazonaws.com`
- **Database Type**: PostGIS (PostgreSQL with spatial extensions)
- **Environment**: `dev-shared-staging`
- **Kafka Cluster**: `kafka-dev-shared-staging`

## Components

### 1. PostGIS Source Connector
- **Name**: `location-platform-postgis-source-connector`
- **Type**: Debezium PostgreSQL CDC Connector
- **Purpose**: Captures changes from the Location Platform production database
- **Topics**: Uses existing topics with prefix `location_platform_dbz_postgres_prod`

### 2. PostGIS Destination Connector  
- **Name**: `location-platform-postgis-sink-connector`
- **Type**: Debezium JDBC Sink Connector
- **Purpose**: Replicates changes to the encrypted Location Platform database
- **Mode**: Upsert mode with primary key handling

### 3. Kafka User
- **Username**: `kafka-connect-location-platform-staging`
- **Permissions**: ACLs for reading/writing to location platform topics
- **Authentication**: SCRAM-SHA-512

### 4. Kafka Connect UI
- **Purpose**: Web interface for monitoring and managing connectors
- **Access**: Available via service endpoint

## Prerequisites

1. **Kafka Topics**: Topics already exist in the k8s-tenant-kafka-resources repository:
   ```
   /Users/<USER>/workspace/k8s-tenant-kafka-resources/clusters/dev-shared-services/kustomize/kafka-resources/topics/kafka-connect-replicator/storage-operations
   ```

2. **Database Access**: 
   - User: `srv_kafka_connect_repl`
   - Password: `careem123`
   - Database: `locationPlatformDb`

3. **Kafka Cluster**: `kafka-dev-shared-staging` must be running

## Installation

```bash
# Install the chart
helm install location-platform-staging ./chart \
  --namespace kafka-resources \
  --create-namespace

# Upgrade the chart
helm upgrade location-platform-staging ./chart \
  --namespace kafka-resources
```

## Configuration

The chart is pre-configured for the Location Platform databases. Key settings in `values.yaml`:

```yaml
locationPlatformSource:
  enabled: true
  hostname: location-platform-db-prod.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
  user: srv_kafka_connect_repl
  password: careem123
  dbname: locationPlatformDb

locationPlatformDestination:
  enabled: true
  hostname: location-platform-db-enc.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
  user: srv_kafka_connect_repl
  password: careem123
  dbname: locationPlatformDb
```

## Monitoring

- **Kafka Connect UI**: Access via `kafka-connect-ui-service` endpoint
- **Connector Status**: `kubectl get kafkaconnectors -n kafka-resources`
- **Logs**: `kubectl logs -n kafka-resources -l app=kafka-connect`
- **User Status**: `kubectl get kafkausers -n kafka-resources`

## Topics Used

The connectors use existing topics:
- `location_platform_dbz_postgres_prod.*` - Main data topics
- `__debezium-heartbeat.location_platform_dbz_postgres_prod` - Heartbeat topic
- `location_platform_dbz_postgres_prod_schema_history` - Schema history topic

## Troubleshooting

1. **Connection Issues**: Verify database connectivity and credentials
2. **Permission Issues**: Check KafkaUser ACLs in `kafka-resources` namespace
3. **Topic Issues**: Ensure all required topics exist and are accessible
4. **Replication Slot**: Check PostgreSQL replication slot status

## Security

- Database passwords are stored in connector configurations (consider using secrets)
- Kafka authentication uses SCRAM-SHA-512
- SSL/TLS enabled for database connections
