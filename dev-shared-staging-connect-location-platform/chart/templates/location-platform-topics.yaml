{{- $configs := .Values.debeziumConfigs -}}
{{- $kafka := .Values.kafka -}}

# Location Platform CDC Topics - matching what the connector actually writes to
{{- if .Values.locationPlatformSource.enabled }}

---
# Addresses table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-addresses
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.addresses"
  partitions: 3
  replicas: 2
  config: {}

---
# Location table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-location
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.location"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Location updates table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-location-updates
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.location_updates"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Companies table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-companies
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.companies"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Flyway schema history table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-flyway-schema-history
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.flyway_schema_history"
  partitions: 1
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Merchants table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-merchants
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.merchants"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Saved locations table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-saved-locations
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.saved_locations"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Spatial ref sys table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-spatial-ref-sys
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.spatial_ref_sys"
  partitions: 1
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

---
# Change events table topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ $kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
    "careem.com/force-allow-delete": "true"
  name: {{ $configs.topicsPrefix | replace "_" "-" }}-public-change-events
  namespace: {{ .Values.namespace }}
spec:
  topicName: "{{ $configs.topicsPrefix }}{{ $configs.instanceName }}.{{ $.Values.locationPlatformSource.dbname }}.public.change_events"
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000  # 7 days
    segment.ms: 86400000     # 1 day

{{- end }}
