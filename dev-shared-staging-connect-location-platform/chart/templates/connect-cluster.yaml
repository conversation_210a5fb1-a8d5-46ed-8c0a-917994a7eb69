{{- $configs := include "connect.configs" . | fromYaml }}

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: {{ $configs.clusterName }}
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  image: docker.io/shqear/debezium:latest
  version: 3.7.0
  replicas: 1
  bootstrapServers: {{ .Values.kafkaConnect.bootstrapServers }}

  # Resource configuration for CDC workload
  resources:
    requests:
      memory: "4Gi"
      cpu: "1000m"
    limits:
      memory: "8Gi"
      cpu: "2000m"
  authentication:
    type: scram-sha-512
    username: {{ .Values.kafkaConnect.authentication.username }}
    passwordSecret:
      secretName: {{ .Values.kafkaConnect.authentication.passwordSecretName }}
      password: {{ .Values.kafkaConnect.authentication.passwordFieldName }}

  config:
    group.id: "{{ $configs.groupId }}"

    # -1 means it will use the default replication factor configured in the broker
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1

    # Topic to use for storing offsets.
    offset.storage.topic: "{{ $configs.internalTopics.offsets }}"

    # Topic to use for storing connector and task configurations
    config.storage.topic: "{{ $configs.internalTopics.configs }}"

    # Topic to use for storing statuses.
    status.storage.topic: "{{ $configs.internalTopics.status }}"
