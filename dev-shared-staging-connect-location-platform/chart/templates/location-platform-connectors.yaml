{{- $configs := include "connect.configs" . | fromYaml }}

{{- if .Values.locationPlatformSource.enabled }}
---
# Location Platform PostGIS CDC Source Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: location-platform-postgis-source-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.postgresql.PostgresConnector
  config:
    connector.class: io.debezium.connector.postgresql.PostgresConnector

    # Avro serialization with Schema Registry
    key.converter: io.confluent.connect.avro.AvroConverter
    key.converter.schema.registry.url: http://staging.kafka.shared-dev.eu-west-1.careem-tech.com:8081
    key.converter.enhanced.avro.schema.support: true
    value.converter: io.confluent.connect.avro.AvroConverter
    value.converter.schema.registry.url: http://staging.kafka.shared-dev.eu-west-1.careem-tech.com:8081
    value.converter.enhanced.avro.schema.support: true

    # Kafka producer compression - reduced for lower resource usage
    producer.compression.type: snappy
    producer.batch.size: 16384
    producer.linger.ms: 20
    producer.buffer.memory: 33554432

    database.hostname: "{{ .Values.locationPlatformSource.hostname }}"
    database.port: "{{ .Values.locationPlatformSource.port }}"
    database.user: "{{ .Values.locationPlatformSource.user }}"
    database.password: "{{ .Values.locationPlatformSource.password }}"
    database.dbname: "{{ .Values.locationPlatformSource.dbname }}"
    database.server.name: "{{ .Values.debeziumConfigs.instanceName }}.{{ .Values.locationPlatformSource.dbname }}"

    # Logical decoding configuration
    plugin.name: "pgoutput"
    slot.name: "location_platform_debezium_slot"
    publication.name: "location_platform_publication"
    publication.autocreate.mode: "filtered"

    # Table configuration - all public schema tables
    schema.include.list: "public"
    table.include.list: "public.*"

    # Snapshot configuration - changed to avoid restarting snapshot on config changes
    snapshot.mode: "when_needed"

    # Topic configuration - using improved naming pattern: connectflow_<service>_<database>_dbz.<schema>.<table>
    topic.prefix: "{{ include "debezium.topicPrefix" . }}"

    # PostGIS-specific configurations
    replica.identity.autoset.values: "public.*:FULL"
    tombstones.on.delete: "false"

    # Handle spatial data types properly
    binary.handling.mode: "base64"

    # SSL configuration for RDS
    database.sslmode: "require"

    # Schema history configuration
    schema.history.internal.kafka.bootstrap.servers: "{{ .Values.kafkaConnect.bootstrapServers }}"
    schema.history.internal.kafka.topic: "{{ include "debezium.schemaHistoryTopic" . }}"
    schema.history.internal.consumer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.consumer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.consumer.sasl.jaas.config: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"{{ .Values.kafkaConnect.authentication.username }}\" password=\"${file:/opt/kafka/connect-password/{{ .Values.kafkaConnect.authentication.passwordFieldName }}:{{ .Values.kafkaConnect.authentication.passwordFieldName }}}\";"
    schema.history.internal.producer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.producer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.producer.sasl.jaas.config: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"{{ .Values.kafkaConnect.authentication.username }}\" password=\"${file:/opt/kafka/connect-password/{{ .Values.kafkaConnect.authentication.passwordFieldName }}:{{ .Values.kafkaConnect.authentication.passwordFieldName }}}\";"

  tasksMax: 1
{{- end }}

{{- if .Values.locationPlatformDestination.enabled }}
---
# Location Platform PostGIS Multi-Table JDBC Sink Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: location-platform-postgis-sink-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.jdbc.JdbcSinkConnector
  config:
    connector.class: io.debezium.connector.jdbc.JdbcSinkConnector

    # Avro deserialization with Schema Registry
    key.converter: io.confluent.connect.avro.AvroConverter
    key.converter.schema.registry.url: http://staging.kafka.shared-dev.eu-west-1.careem-tech.com:8081
    key.converter.enhanced.avro.schema.support: true
    value.converter: io.confluent.connect.avro.AvroConverter
    value.converter.schema.registry.url: http://staging.kafka.shared-dev.eu-west-1.careem-tech.com:8081
    value.converter.enhanced.avro.schema.support: true

    # Kafka consumer optimizations - reduced for lower resource usage
    consumer.fetch.min.bytes: 524288
    consumer.fetch.max.wait.ms: 1000
    consumer.max.poll.records: 500

    connection.url: "jdbc:postgresql://{{ .Values.locationPlatformDestination.hostname }}:{{ .Values.locationPlatformDestination.port }}/{{ .Values.locationPlatformDestination.dbname }}?sslmode=require"
    connection.username: "{{ .Values.locationPlatformDestination.user }}"
    connection.password: "{{ .Values.locationPlatformDestination.password }}"

    # Hibernate/JPA configuration for JDBC Sink Connector
    hibernate.dialect: "org.hibernate.dialect.PostgreSQLDialect"
    jakarta.persistence.jdbc.url: "jdbc:postgresql://{{ .Values.locationPlatformDestination.hostname }}:{{ .Values.locationPlatformDestination.port }}/{{ .Values.locationPlatformDestination.dbname }}?sslmode=require"
    jakarta.persistence.jdbc.user: "{{ .Values.locationPlatformDestination.user }}"
    jakarta.persistence.jdbc.password: "{{ .Values.locationPlatformDestination.password }}"

    # Multi-topic configuration - handles all tables from the Location Platform PostGIS CDC source
    # Pattern: connectflow_<service>_<database>_dbz.public.*
    topics.regex: "{{ include "debezium.topicPrefix" . }}\\.public\\..*"

    # Use ByLogicalTableRouter transform for dynamic table name mapping
    transforms: "route"
    transforms.route.type: "io.debezium.transforms.ByLogicalTableRouter"
    transforms.route.topic.regex: "{{ include "debezium.topicPrefix" . }}\\.public\\.(.*)"
    transforms.route.topic.replacement: "$1"
    transforms.route.key.enforce.uniqueness: "false"

    # Connector behavior - back to upsert mode since flyway_schema_history has a primary key
    insert.mode: "upsert"
    delete.enabled: "true"
    primary.key.mode: "record_key"
    # Note: No primary.key.fields specified - let connector auto-detect from record key
    schema.evolution: "basic"

    # Table handling
    auto.create: "true"
    auto.evolve: "true"

    # Performance optimizations - reduced for lower resource usage
    batch.size: "500"
    use.reduction.buffer: "true"

    # Connection pool settings - reduced for lower resource usage
    connection.pool.min_size: "1"
    connection.pool.max_size: "5"
    connection.pool.acquire_increment: "1"

    # PostGIS-specific settings for spatial data handling
    quote.sql.identifiers: "always"

  # Single task for simpler resource management
  tasksMax: 1
{{- end }}
