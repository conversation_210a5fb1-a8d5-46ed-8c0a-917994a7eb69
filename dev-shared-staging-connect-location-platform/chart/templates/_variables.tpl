{{- define "connect.configs" -}}
internalTopicsPrefix: "{{ .Release.Name }}-"

internalTopics:
    configs: "{{ .Release.Name }}-configs"
    offsets: "{{ .Release.Name }}-offsets"
    status: "{{ .Release.Name }}-status"

groupId: "{{ .Release.Name }}-group"

clusterName:  {{ .Release.Name }}-cluster
{{- end }}

{{/*
Generate topic prefix for Debezium connectors
Usage: {{ include "debezium.topicPrefix" . }}
*/}}
{{- define "debezium.topicPrefix" -}}
{{ .Values.debeziumConfigs.topicsPrefix }}_{{ .Values.debeziumConfigs.instanceName }}_{{ .Values.locationPlatformSource.dbname }}_dbz
{{- end }}

{{/*
Generate full topic name for a table
Usage: {{ include "debezium.topicName" (dict "Values" .Values "tableName" "addresses") }}
*/}}
{{- define "debezium.topicName" -}}
{{ include "debezium.topicPrefix" . }}.{{ .Values.locationPlatformSource.schemaName }}.{{ .tableName }}
{{- end }}

{{/*
Generate schema history topic name
Usage: {{ include "debezium.schemaHistoryTopic" . }}
*/}}
{{- define "debezium.schemaHistoryTopic" -}}
{{ include "debezium.topicPrefix" . }}_schema_history
{{- end }}
