{{- $configs := include "connect.configs" . | fromYaml }}

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: {{ .Values.kafkaConnect.authentication.username }}
  namespace: {{ .Values.kafka.resourcesNamespace }}
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
  annotations:
    careem.com/aws-secret-account-name: dev-rh
    careem.com/aws-secret-enable: 'false'
    careem.com/aws-secret-name: storage/staging/location-platform/kafka-connect
    careem.com/aws-secret-pass-key-name: kafka_connect_password
    careem.com/aws-secret-user-key-name: kafka_connect_username
    careem.com/force-allow-delete: 'true'
spec:
  authentication:
    type: scram-sha-512
  authorization:
    type: simple
    acls:

      # Allow to list all topics
      - resource:
          type: topic
          name: "*"
          patternType: literal
        operations:
          - Describe

      # Allow to read and write to all connect topics
      - resource:
          type: topic
          name: "{{ .Release.Name }}-"
          patternType: prefix
        operations:
          - Read
          - Write
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector to write to topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}"
          patternType: prefix
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector heartbeat topic
      - resource:
          type: topic
          name: "__debezium-heartbeat.{{ .Values.debeziumConfigs.topicsPrefix }}"
          patternType: literal
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector schema history topic
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}schema-history"
          patternType: literal
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow Location Platform PostGIS CDC Source Connector consumer groups
      - resource:
          type: group
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}"
          patternType: prefix
        operations:
          - Read

      # Allow Location Platform PostGIS Multi-Topic JDBC Sink Connector to read from topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}"
          patternType: prefix
        operations:
          - Read
          - DescribeConfigs

      # Allow Location Platform PostGIS Multi-Topic JDBC Sink Connector consumer group
      - resource:
          type: group
          name: "connect-location-platform-postgis-sink-connector"
          patternType: literal
        operations:
          - Read

      # Allow to access connect group
      - resource:
          type: group
          name: {{ $configs.groupId }}
          patternType: literal
        operations:
          - Read

      - resource:
          type: group
          name: {{ $configs.groupId }}-
          patternType: prefix
        operations:
          - Read
          - Write
        host: "*"
