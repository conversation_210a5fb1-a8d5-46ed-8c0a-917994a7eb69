{{- $configs := include "connect.configs" . | fromYaml }}

---
# Kafka Connect Internal Topics - Configs
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: {{ $configs.internalTopics.configs }}
  namespace: {{ .Values.kafka.resourcesNamespace }}
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 1
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1

---
# Kafka Connect Internal Topics - Offsets
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: {{ $configs.internalTopics.offsets }}
  namespace: {{ .Values.kafka.resourcesNamespace }}
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 25
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1

---
# Kafka Connect Internal Topics - Status
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: {{ $configs.internalTopics.status }}
  namespace: {{ .Values.kafka.resourcesNamespace }}
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 5
  replicas: 2
  config:
    cleanup.policy: compact
    compression.type: producer
    min.insync.replicas: 1

# Note: Schema history topic already exists in Kafka cluster
# location_platform_dbz_postgres_prod_schema_history - no need to create
