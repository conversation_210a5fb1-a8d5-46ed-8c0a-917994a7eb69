#!/bin/bash -e

# Test the Location Platform staging chart template
helm template location-platform-staging . \
--namespace kafka-resources \
-f values.yaml \
--kube-version 1.26 \
--api-versions kafka.strimzi.io/v1beta2 \
--api-versions kafka.strimzi.io/v1beta2/KafkaConnect \
--api-versions kafka.strimzi.io/v1beta2/KafkaConnector \
--api-versions kafka.strimzi.io/v1beta2/KafkaUser \
--api-versions apps/v1 \
--api-versions apps/v1/Deployment \
--api-versions v1 \
--api-versions v1/ConfigMap \
--api-versions v1/Secret \
--api-versions v1/Service \
--include-crds

echo "Location Platform staging chart template test completed successfully!"
