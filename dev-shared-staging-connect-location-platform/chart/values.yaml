global:
  serviceName: storage-qa

kafka:
  clusterName: kafka-dev-shared-staging
  resourcesNamespace: kafka-resources
  clusterNamespace: kafka

kafkaConnect:
  bootstrapServers: staging.kafka.shared-dev.eu-west-1.careem-tech.com:9094
  authentication:
    username: kafka-connect-location-platform-staging
    passwordSecretName: kafka-user-kafka-connect-location-platform-staging
    passwordFieldName: password

debeziumConfigs:
  groupId: kafka-connect-location-platform-staging-
  topicsPrefix: connectflow
  instanceName: location-platform  # Unique identifier for this CDC instance

# Location Platform PostGIS Source Database (Production RDS)
locationPlatformSource:
  enabled: true
  hostname: location-platform-db-prod.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
  port: 5432
  user: srv_kafka_connect_repl
  password: careem123
  dbname: locationPlatformDb
  schemaName: public  # PostgreSQL schema name

# Location Platform PostGIS Destination Database (Encrypted RDS)
locationPlatformDestination:
  enabled: true
  hostname: location-platform-db-enc.cgevunlz3bfz.eu-west-1.rds.amazonaws.com
  port: 5432
  user: srv_kafka_connect_repl
  password: careem123
  dbname: locationPlatformDb

# KafkaConnect UI Configuration (Connect-only UI)
kafkaConnectUI:
  enabled: true
  image: landoop/kafka-connect-ui:latest
  port: 8000

# Namespace
namespace: kafka-resources
