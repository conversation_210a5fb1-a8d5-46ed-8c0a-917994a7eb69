{{- if .Values.mysql.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-test
data:
  mysql.cnf: |
    [mysqld]
    server-id = 223344
    log-bin = mysql-bin
    binlog_format = ROW
    binlog_row_image = FULL
    expire_logs_days = 10
    # Enable GTID mode (optional but recommended)
    gtid_mode = ON
    enforce_gtid_consistency = ON
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-test
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql-test
  template:
    metadata:
      labels:
        app: mysql-test
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
          name: mysql
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "rootpassword123"
        - name: MYSQL_DATABASE
          value: "testdb"
        volumeMounts:
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: mysql-config
        configMap:
          name: mysql-config

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-test-service
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-test
spec:
  ports:
  - port: 3306
    targetPort: 3306
    name: mysql
  selector:
    app: mysql-test
  type: ClusterIP
{{- end }}
