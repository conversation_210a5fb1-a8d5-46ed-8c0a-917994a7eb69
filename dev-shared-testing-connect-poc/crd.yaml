{{/*apiVersion: apiextensions.k8s.io/v1*/}}
{{/*kind: CustomResourceDefinition*/}}
{{/*metadata:*/}}
{{/*  name: kafkaconnects.kafka.strimzi.io*/}}
{{/*  uid: 50ca0729-68cf-431c-8df5-43a7ab06bc7a*/}}
{{/*  resourceVersion: '2893818655'*/}}
{{/*  generation: 3*/}}
{{/*  creationTimestamp: '2024-03-26T08:27:45Z'*/}}
{{/*  labels:*/}}
{{/*    app: strimzi*/}}
{{/*    component: kafkaconnects.kafka.strimzi.io-crd*/}}
{{/*    strimzi.io/crd-install: 'true'*/}}
{{/*  selfLink: >-*/}}
{{/*    /apis/apiextensions.k8s.io/v1/customresourcedefinitions/kafkaconnects.kafka.strimzi.io*/}}
{{/*status:*/}}
{{/*  conditions:*/}}
{{/*    - type: NamesAccepted*/}}
{{/*      status: 'True'*/}}
{{/*      lastTransitionTime: '2024-03-26T08:27:45Z'*/}}
{{/*      reason: NoConflicts*/}}
{{/*      message: no conflicts found*/}}
{{/*    - type: Established*/}}
{{/*      status: 'True'*/}}
{{/*      lastTransitionTime: '2024-03-26T08:27:45Z'*/}}
{{/*      reason: InitialNamesAccepted*/}}
{{/*      message: the initial names have been accepted*/}}
{{/*  acceptedNames:*/}}
{{/*    plural: kafkaconnects*/}}
{{/*    singular: kafkaconnect*/}}
{{/*    shortNames:*/}}
{{/*      - kc*/}}
{{/*    kind: KafkaConnect*/}}
{{/*    listKind: KafkaConnectList*/}}
{{/*    categories:*/}}
{{/*      - strimzi*/}}
{{/*  storedVersions:*/}}
{{/*    - v1beta2*/}}
{{/*spec:*/}}
{{/*  group: kafka.strimzi.io*/}}
{{/*  names:*/}}
{{/*    plural: kafkaconnects*/}}
{{/*    singular: kafkaconnect*/}}
{{/*    shortNames:*/}}
{{/*      - kc*/}}
{{/*    kind: KafkaConnect*/}}
{{/*    listKind: KafkaConnectList*/}}
{{/*    categories:*/}}
{{/*      - strimzi*/}}
{{/*  scope: Namespaced*/}}
{{/*  versions:*/}}
{{/*    - name: v1beta2*/}}
{{/*      served: true*/}}
{{/*      storage: true*/}}
{{/*      schema:*/}}
{{/*        openAPIV3Schema:*/}}
{{/*          type: object*/}}
{{/*          properties:*/}}
{{/*            apiVersion:*/}}
{{/*              description: >-*/}}
{{/*                APIVersion defines the versioned schema of this representation*/}}
{{/*                of an object. Servers should convert recognized schemas to the*/}}
{{/*                latest internal value, and may reject unrecognized values. More*/}}
{{/*                info:*/}}
{{/*                https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources*/}}
{{/*              type: string*/}}
{{/*            kind:*/}}
{{/*              description: >-*/}}
{{/*                Kind is a string value representing the REST resource this*/}}
{{/*                object represents. Servers may infer this from the endpoint the*/}}
{{/*                client submits requests to. Cannot be updated. In CamelCase.*/}}
{{/*                More info:*/}}
{{/*                https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds*/}}
{{/*              type: string*/}}
{{/*            metadata:*/}}
{{/*              type: object*/}}
{{/*            spec:*/}}
{{/*              description: The specification of the Kafka Connect cluster.*/}}
{{/*              type: object*/}}
{{/*              required:*/}}
{{/*                - bootstrapServers*/}}
{{/*              properties:*/}}
{{/*                authentication:*/}}
{{/*                  description: Authentication configuration for Kafka Connect.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - type*/}}
{{/*                  properties:*/}}
{{/*                    accessToken:*/}}
{{/*                      description: >-*/}}
{{/*                        Link to Kubernetes Secret containing the access token*/}}
{{/*                        which was obtained from the authorization server.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - key*/}}
{{/*                        - secretName*/}}
{{/*                      properties:*/}}
{{/*                        key:*/}}
{{/*                          description: >-*/}}
{{/*                            The key under which the secret value is stored in*/}}
{{/*                            the Kubernetes Secret.*/}}
{{/*                          type: string*/}}
{{/*                        secretName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the Kubernetes Secret containing the*/}}
{{/*                            secret value.*/}}
{{/*                          type: string*/}}
{{/*                    accessTokenIsJwt:*/}}
{{/*                      description: >-*/}}
{{/*                        Configure whether access token should be treated as JWT.*/}}
{{/*                        This should be set to `false` if the authorization*/}}
{{/*                        server returns opaque tokens. Defaults to `true`.*/}}
{{/*                      type: boolean*/}}
{{/*                    audience:*/}}
{{/*                      description: >-*/}}
{{/*                        OAuth audience to use when authenticating against the*/}}
{{/*                        authorization server. Some authorization servers require*/}}
{{/*                        the audience to be explicitly set. The possible values*/}}
{{/*                        depend on how the authorization server is configured. By*/}}
{{/*                        default, `audience` is not specified when performing the*/}}
{{/*                        token endpoint request.*/}}
{{/*                      type: string*/}}
{{/*                    certificateAndKey:*/}}
{{/*                      description: >-*/}}
{{/*                        Reference to the `Secret` which holds the certificate*/}}
{{/*                        and private key pair.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - secretName*/}}
{{/*                        - certificate*/}}
{{/*                        - key*/}}
{{/*                      properties:*/}}
{{/*                        certificate:*/}}
{{/*                          description: The name of the file certificate in the Secret.*/}}
{{/*                          type: string*/}}
{{/*                        key:*/}}
{{/*                          description: The name of the private key in the Secret.*/}}
{{/*                          type: string*/}}
{{/*                        secretName:*/}}
{{/*                          description: The name of the Secret containing the certificate.*/}}
{{/*                          type: string*/}}
{{/*                    clientId:*/}}
{{/*                      description: >-*/}}
{{/*                        OAuth Client ID which the Kafka client can use to*/}}
{{/*                        authenticate against the OAuth server and use the token*/}}
{{/*                        endpoint URI.*/}}
{{/*                      type: string*/}}
{{/*                    clientSecret:*/}}
{{/*                      description: >-*/}}
{{/*                        Link to Kubernetes Secret containing the OAuth client*/}}
{{/*                        secret which the Kafka client can use to authenticate*/}}
{{/*                        against the OAuth server and use the token endpoint URI.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - key*/}}
{{/*                        - secretName*/}}
{{/*                      properties:*/}}
{{/*                        key:*/}}
{{/*                          description: >-*/}}
{{/*                            The key under which the secret value is stored in*/}}
{{/*                            the Kubernetes Secret.*/}}
{{/*                          type: string*/}}
{{/*                        secretName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the Kubernetes Secret containing the*/}}
{{/*                            secret value.*/}}
{{/*                          type: string*/}}
{{/*                    connectTimeoutSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The connect timeout in seconds when connecting to*/}}
{{/*                        authorization server. If not set, the effective connect*/}}
{{/*                        timeout is 60 seconds.*/}}
{{/*                      type: integer*/}}
{{/*                    disableTlsHostnameVerification:*/}}
{{/*                      description: >-*/}}
{{/*                        Enable or disable TLS hostname verification. Default*/}}
{{/*                        value is `false`.*/}}
{{/*                      type: boolean*/}}
{{/*                    enableMetrics:*/}}
{{/*                      description: >-*/}}
{{/*                        Enable or disable OAuth metrics. Default value is*/}}
{{/*                        `false`.*/}}
{{/*                      type: boolean*/}}
{{/*                    httpRetries:*/}}
{{/*                      description: >-*/}}
{{/*                        The maximum number of retries to attempt if an initial*/}}
{{/*                        HTTP request fails. If not set, the default is to not*/}}
{{/*                        attempt any retries.*/}}
{{/*                      type: integer*/}}
{{/*                    httpRetryPauseMs:*/}}
{{/*                      description: >-*/}}
{{/*                        The pause to take before retrying a failed HTTP request.*/}}
{{/*                        If not set, the default is to not pause at all but to*/}}
{{/*                        immediately repeat a request.*/}}
{{/*                      type: integer*/}}
{{/*                    includeAcceptHeader:*/}}
{{/*                      description: >-*/}}
{{/*                        Whether the Accept header should be set in requests to*/}}
{{/*                        the authorization servers. The default value is `true`.*/}}
{{/*                      type: boolean*/}}
{{/*                    maxTokenExpirySeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        Set or limit time-to-live of the access tokens to the*/}}
{{/*                        specified number of seconds. This should be set if the*/}}
{{/*                        authorization server returns opaque tokens.*/}}
{{/*                      type: integer*/}}
{{/*                    passwordSecret:*/}}
{{/*                      description: Reference to the `Secret` which holds the password.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - secretName*/}}
{{/*                        - password*/}}
{{/*                      properties:*/}}
{{/*                        password:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the key in the Secret under which the*/}}
{{/*                            password is stored.*/}}
{{/*                          type: string*/}}
{{/*                        secretName:*/}}
{{/*                          description: The name of the Secret containing the password.*/}}
{{/*                          type: string*/}}
{{/*                    readTimeoutSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The read timeout in seconds when connecting to*/}}
{{/*                        authorization server. If not set, the effective read*/}}
{{/*                        timeout is 60 seconds.*/}}
{{/*                      type: integer*/}}
{{/*                    refreshToken:*/}}
{{/*                      description: >-*/}}
{{/*                        Link to Kubernetes Secret containing the refresh token*/}}
{{/*                        which can be used to obtain access token from the*/}}
{{/*                        authorization server.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - key*/}}
{{/*                        - secretName*/}}
{{/*                      properties:*/}}
{{/*                        key:*/}}
{{/*                          description: >-*/}}
{{/*                            The key under which the secret value is stored in*/}}
{{/*                            the Kubernetes Secret.*/}}
{{/*                          type: string*/}}
{{/*                        secretName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the Kubernetes Secret containing the*/}}
{{/*                            secret value.*/}}
{{/*                          type: string*/}}
{{/*                    scope:*/}}
{{/*                      description: >-*/}}
{{/*                        OAuth scope to use when authenticating against the*/}}
{{/*                        authorization server. Some authorization servers require*/}}
{{/*                        this to be set. The possible values depend on how*/}}
{{/*                        authorization server is configured. By default `scope`*/}}
{{/*                        is not specified when doing the token endpoint request.*/}}
{{/*                      type: string*/}}
{{/*                    tlsTrustedCertificates:*/}}
{{/*                      description: >-*/}}
{{/*                        Trusted certificates for TLS connection to the OAuth*/}}
{{/*                        server.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        required:*/}}
{{/*                          - secretName*/}}
{{/*                        oneOf:*/}}
{{/*                          - required:*/}}
{{/*                              - certificate*/}}
{{/*                            properties:*/}}
{{/*                              certificate: {}*/}}
{{/*                          - required:*/}}
{{/*                              - pattern*/}}
{{/*                            properties:*/}}
{{/*                              pattern: {}*/}}
{{/*                        properties:*/}}
{{/*                          certificate:*/}}
{{/*                            description: The name of the file certificate in the secret.*/}}
{{/*                            type: string*/}}
{{/*                          pattern:*/}}
{{/*                            description: >-*/}}
{{/*                              Pattern for the certificate files in the secret.*/}}
{{/*                              Use the*/}}
{{/*                              link:https://en.wikipedia.org/wiki/Glob_(programming)[_glob*/}}
{{/*                              syntax_] for the pattern. All files in the secret*/}}
{{/*                              that match the pattern are used.*/}}
{{/*                            type: string*/}}
{{/*                          secretName:*/}}
{{/*                            description: The name of the Secret containing the certificate.*/}}
{{/*                            type: string*/}}
{{/*                    tokenEndpointUri:*/}}
{{/*                      description: Authorization server token endpoint URI.*/}}
{{/*                      type: string*/}}
{{/*                    type:*/}}
{{/*                      description: >-*/}}
{{/*                        Authentication type. Currently the supported types are*/}}
{{/*                        `tls`, `scram-sha-256`, `scram-sha-512`, `plain`, and*/}}
{{/*                        'oauth'. `scram-sha-256` and `scram-sha-512` types use*/}}
{{/*                        SASL SCRAM-SHA-256 and SASL SCRAM-SHA-512*/}}
{{/*                        Authentication, respectively. `plain` type uses SASL*/}}
{{/*                        PLAIN Authentication. `oauth` type uses SASL OAUTHBEARER*/}}
{{/*                        Authentication. The `tls` type uses TLS Client*/}}
{{/*                        Authentication. The `tls` type is supported only over*/}}
{{/*                        TLS connections.*/}}
{{/*                      type: string*/}}
{{/*                      enum:*/}}
{{/*                        - tls*/}}
{{/*                        - scram-sha-256*/}}
{{/*                        - scram-sha-512*/}}
{{/*                        - plain*/}}
{{/*                        - oauth*/}}
{{/*                    username:*/}}
{{/*                      description: Username used for the authentication.*/}}
{{/*                      type: string*/}}
{{/*                bootstrapServers:*/}}
{{/*                  description: >-*/}}
{{/*                    Bootstrap servers to connect to. This should be given as a*/}}
{{/*                    comma separated list of _<hostname>_:_<port>_ pairs.*/}}
{{/*                  type: string*/}}
{{/*                build:*/}}
{{/*                  description: >-*/}}
{{/*                    Configures how the Connect container image should be built.*/}}
{{/*                    Optional.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - output*/}}
{{/*                    - plugins*/}}
{{/*                  properties:*/}}
{{/*                    output:*/}}
{{/*                      description: >-*/}}
{{/*                        Configures where should the newly built image be stored.*/}}
{{/*                        Required.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - image*/}}
{{/*                        - type*/}}
{{/*                      properties:*/}}
{{/*                        additionalKanikoOptions:*/}}
{{/*                          description: >-*/}}
{{/*                            Configures additional options which will be passed*/}}
{{/*                            to the Kaniko executor when building the new Connect*/}}
{{/*                            image. Allowed options are: --customPlatform,*/}}
{{/*                            --insecure, --insecure-pull, --insecure-registry,*/}}
{{/*                            --log-format, --log-timestamp, --registry-mirror,*/}}
{{/*                            --reproducible, --single-snapshot,*/}}
{{/*                            --skip-tls-verify, --skip-tls-verify-pull,*/}}
{{/*                            --skip-tls-verify-registry, --verbosity,*/}}
{{/*                            --snapshotMode, --use-new-run. These options will be*/}}
{{/*                            used only on Kubernetes where the Kaniko executor is*/}}
{{/*                            used. They will be ignored on OpenShift. The options*/}}
{{/*                            are described in the*/}}
{{/*                            link:https://github.com/GoogleContainerTools/kaniko[Kaniko*/}}
{{/*                            GitHub repository^]. Changing this field does not*/}}
{{/*                            trigger new build of the Kafka Connect image.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: string*/}}
{{/*                        image:*/}}
{{/*                          description: The name of the image which will be built. Required.*/}}
{{/*                          type: string*/}}
{{/*                        pushSecret:*/}}
{{/*                          description: >-*/}}
{{/*                            Container Registry Secret with the credentials for*/}}
{{/*                            pushing the newly built image.*/}}
{{/*                          type: string*/}}
{{/*                        type:*/}}
{{/*                          description: >-*/}}
{{/*                            Output type. Must be either `docker` for pushing the*/}}
{{/*                            newly build image to Docker compatible registry or*/}}
{{/*                            `imagestream` for pushing the image to OpenShift*/}}
{{/*                            ImageStream. Required.*/}}
{{/*                          type: string*/}}
{{/*                          enum:*/}}
{{/*                            - docker*/}}
{{/*                            - imagestream*/}}
{{/*                    plugins:*/}}
{{/*                      description: >-*/}}
{{/*                        List of connector plugins which should be added to the*/}}
{{/*                        Kafka Connect. Required.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        required:*/}}
{{/*                          - name*/}}
{{/*                          - artifacts*/}}
{{/*                        properties:*/}}
{{/*                          artifacts:*/}}
{{/*                            description: >-*/}}
{{/*                              List of artifacts which belong to this connector*/}}
{{/*                              plugin. Required.*/}}
{{/*                            type: array*/}}
{{/*                            items:*/}}
{{/*                              type: object*/}}
{{/*                              required:*/}}
{{/*                                - type*/}}
{{/*                              properties:*/}}
{{/*                                artifact:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Maven artifact id. Applicable to the `maven`*/}}
{{/*                                    artifact type only.*/}}
{{/*                                  type: string*/}}
{{/*                                fileName:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Name under which the artifact will be*/}}
{{/*                                    stored.*/}}
{{/*                                  type: string*/}}
{{/*                                group:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Maven group id. Applicable to the `maven`*/}}
{{/*                                    artifact type only.*/}}
{{/*                                  type: string*/}}
{{/*                                insecure:*/}}
{{/*                                  description: >-*/}}
{{/*                                    By default, connections using TLS are*/}}
{{/*                                    verified to check they are secure. The*/}}
{{/*                                    server certificate used must be valid,*/}}
{{/*                                    trusted, and contain the server name. By*/}}
{{/*                                    setting this option to `true`, all TLS*/}}
{{/*                                    verification is disabled and the artifact*/}}
{{/*                                    will be downloaded, even when the server is*/}}
{{/*                                    considered insecure.*/}}
{{/*                                  type: boolean*/}}
{{/*                                repository:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Maven repository to download the artifact*/}}
{{/*                                    from. Applicable to the `maven` artifact*/}}
{{/*                                    type only.*/}}
{{/*                                  type: string*/}}
{{/*                                sha512sum:*/}}
{{/*                                  description: >-*/}}
{{/*                                    SHA512 checksum of the artifact. Optional.*/}}
{{/*                                    If specified, the checksum will be verified*/}}
{{/*                                    while building the new container. If not*/}}
{{/*                                    specified, the downloaded artifact will not*/}}
{{/*                                    be verified. Not applicable to the `maven`*/}}
{{/*                                    artifact type.*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Artifact type. Currently, the supported*/}}
{{/*                                    artifact types are `tgz`, `jar`, `zip`,*/}}
{{/*                                    `other` and `maven`.*/}}
{{/*                                  type: string*/}}
{{/*                                  enum:*/}}
{{/*                                    - jar*/}}
{{/*                                    - tgz*/}}
{{/*                                    - zip*/}}
{{/*                                    - maven*/}}
{{/*                                    - other*/}}
{{/*                                url:*/}}
{{/*                                  description: >-*/}}
{{/*                                    URL of the artifact which will be*/}}
{{/*                                    downloaded. Strimzi does not do any security*/}}
{{/*                                    scanning of the downloaded artifacts. For*/}}
{{/*                                    security reasons, you should first verify*/}}
{{/*                                    the artifacts manually and configure the*/}}
{{/*                                    checksum verification to make sure the same*/}}
{{/*                                    artifact is used in the automated build.*/}}
{{/*                                    Required for `jar`, `zip`, `tgz` and `other`*/}}
{{/*                                    artifacts. Not applicable to the `maven`*/}}
{{/*                                    artifact type.*/}}
{{/*                                  type: string*/}}
{{/*                                  pattern: >-*/}}
{{/*                                    ^(https?|ftp)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]$*/}}
{{/*                                version:*/}}
{{/*                                  description: >-*/}}
{{/*                                    Maven version number. Applicable to the*/}}
{{/*                                    `maven` artifact type only.*/}}
{{/*                                  type: string*/}}
{{/*                          name:*/}}
{{/*                            description: >-*/}}
{{/*                              The unique name of the connector plugin. Will be*/}}
{{/*                              used to generate the path where the connector*/}}
{{/*                              artifacts will be stored. The name has to be*/}}
{{/*                              unique within the KafkaConnect resource. The name*/}}
{{/*                              has to follow the following pattern:*/}}
{{/*                              `^[a-z][-_a-z0-9]*[a-z]$`. Required.*/}}
{{/*                            type: string*/}}
{{/*                            pattern: ^[a-z0-9][-_a-z0-9]*[a-z0-9]$*/}}
{{/*                    resources:*/}}
{{/*                      description: CPU and memory resources to reserve for the build.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        claims:*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                type: string*/}}
{{/*                        limits:*/}}
{{/*                          type: object*/}}
{{/*                          additionalProperties:*/}}
{{/*                            pattern: >-*/}}
{{/*                              ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$*/}}
{{/*                            anyOf:*/}}
{{/*                              - type: integer*/}}
{{/*                              - type: string*/}}
{{/*                            x-kubernetes-int-or-string: true*/}}
{{/*                        requests:*/}}
{{/*                          type: object*/}}
{{/*                          additionalProperties:*/}}
{{/*                            pattern: >-*/}}
{{/*                              ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$*/}}
{{/*                            anyOf:*/}}
{{/*                              - type: integer*/}}
{{/*                              - type: string*/}}
{{/*                            x-kubernetes-int-or-string: true*/}}
{{/*                clientRackInitImage:*/}}
{{/*                  description: >-*/}}
{{/*                    The image of the init container used for initializing the*/}}
{{/*                    `client.rack`.*/}}
{{/*                  type: string*/}}
{{/*                config:*/}}
{{/*                  description: >-*/}}
{{/*                    The Kafka Connect configuration. Properties with the*/}}
{{/*                    following prefixes cannot be set: ssl., sasl., security.,*/}}
{{/*                    listeners, plugin.path, rest., bootstrap.servers,*/}}
{{/*                    consumer.interceptor.classes, producer.interceptor.classes*/}}
{{/*                    (with the exception of:*/}}
{{/*                    ssl.endpoint.identification.algorithm, ssl.cipher.suites,*/}}
{{/*                    ssl.protocol, ssl.enabled.protocols).*/}}
{{/*                  type: object*/}}
{{/*                  x-kubernetes-preserve-unknown-fields: true*/}}
{{/*                externalConfiguration:*/}}
{{/*                  description: >-*/}}
{{/*                    Pass data from Secrets or ConfigMaps to the Kafka Connect*/}}
{{/*                    pods and use them to configure connectors.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    env:*/}}
{{/*                      description: >-*/}}
{{/*                        Makes data from a Secret or ConfigMap available in the*/}}
{{/*                        Kafka Connect pods as environment variables.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        required:*/}}
{{/*                          - name*/}}
{{/*                          - valueFrom*/}}
{{/*                        properties:*/}}
{{/*                          name:*/}}
{{/*                            description: >-*/}}
{{/*                              Name of the environment variable which will be*/}}
{{/*                              passed to the Kafka Connect pods. The name of the*/}}
{{/*                              environment variable cannot start with `KAFKA_` or*/}}
{{/*                              `STRIMZI_`.*/}}
{{/*                            type: string*/}}
{{/*                          valueFrom:*/}}
{{/*                            description: >-*/}}
{{/*                              Value of the environment variable which will be*/}}
{{/*                              passed to the Kafka Connect pods. It can be passed*/}}
{{/*                              either as a reference to Secret or ConfigMap*/}}
{{/*                              field. The field has to specify exactly one Secret*/}}
{{/*                              or ConfigMap.*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              configMapKeyRef:*/}}
{{/*                                description: Reference to a key in a ConfigMap.*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  key:*/}}
{{/*                                    type: string*/}}
{{/*                                  name:*/}}
{{/*                                    type: string*/}}
{{/*                                  optional:*/}}
{{/*                                    type: boolean*/}}
{{/*                              secretKeyRef:*/}}
{{/*                                description: Reference to a key in a Secret.*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  key:*/}}
{{/*                                    type: string*/}}
{{/*                                  name:*/}}
{{/*                                    type: string*/}}
{{/*                                  optional:*/}}
{{/*                                    type: boolean*/}}
{{/*                    volumes:*/}}
{{/*                      description: >-*/}}
{{/*                        Makes data from a Secret or ConfigMap available in the*/}}
{{/*                        Kafka Connect pods as volumes.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        required:*/}}
{{/*                          - name*/}}
{{/*                        properties:*/}}
{{/*                          configMap:*/}}
{{/*                            description: >-*/}}
{{/*                              Reference to a key in a ConfigMap. Exactly one*/}}
{{/*                              Secret or ConfigMap has to be specified.*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              defaultMode:*/}}
{{/*                                type: integer*/}}
{{/*                              items:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: object*/}}
{{/*                                  properties:*/}}
{{/*                                    key:*/}}
{{/*                                      type: string*/}}
{{/*                                    mode:*/}}
{{/*                                      type: integer*/}}
{{/*                                    path:*/}}
{{/*                                      type: string*/}}
{{/*                              name:*/}}
{{/*                                type: string*/}}
{{/*                              optional:*/}}
{{/*                                type: boolean*/}}
{{/*                          name:*/}}
{{/*                            description: >-*/}}
{{/*                              Name of the volume which will be added to the*/}}
{{/*                              Kafka Connect pods.*/}}
{{/*                            type: string*/}}
{{/*                          secret:*/}}
{{/*                            description: >-*/}}
{{/*                              Reference to a key in a Secret. Exactly one Secret*/}}
{{/*                              or ConfigMap has to be specified.*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              defaultMode:*/}}
{{/*                                type: integer*/}}
{{/*                              items:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: object*/}}
{{/*                                  properties:*/}}
{{/*                                    key:*/}}
{{/*                                      type: string*/}}
{{/*                                    mode:*/}}
{{/*                                      type: integer*/}}
{{/*                                    path:*/}}
{{/*                                      type: string*/}}
{{/*                              optional:*/}}
{{/*                                type: boolean*/}}
{{/*                              secretName:*/}}
{{/*                                type: string*/}}
{{/*                image:*/}}
{{/*                  description: >-*/}}
{{/*                    The container image used for Kafka Connect pods. If no image*/}}
{{/*                    name is explicitly specified, it is determined based on the*/}}
{{/*                    `spec.version` configuration. The image names are*/}}
{{/*                    specifically mapped to corresponding versions in the Cluster*/}}
{{/*                    Operator configuration.*/}}
{{/*                  type: string*/}}
{{/*                jmxOptions:*/}}
{{/*                  description: JMX Options.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    authentication:*/}}
{{/*                      description: >-*/}}
{{/*                        Authentication configuration for connecting to the JMX*/}}
{{/*                        port.*/}}
{{/*                      type: object*/}}
{{/*                      required:*/}}
{{/*                        - type*/}}
{{/*                      properties:*/}}
{{/*                        type:*/}}
{{/*                          description: >-*/}}
{{/*                            Authentication type. Currently the only supported*/}}
{{/*                            types are `password`.`password` type creates a*/}}
{{/*                            username and protected port with no TLS.*/}}
{{/*                          type: string*/}}
{{/*                          enum:*/}}
{{/*                            - password*/}}
{{/*                jvmOptions:*/}}
{{/*                  description: JVM Options for pods.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    '-XX':*/}}
{{/*                      description: A map of -XX options to the JVM.*/}}
{{/*                      type: object*/}}
{{/*                      additionalProperties:*/}}
{{/*                        type: string*/}}
{{/*                    '-Xms':*/}}
{{/*                      description: '-Xms option to to the JVM.'*/}}
{{/*                      type: string*/}}
{{/*                      pattern: ^[0-9]+[mMgG]?$*/}}
{{/*                    '-Xmx':*/}}
{{/*                      description: '-Xmx option to to the JVM.'*/}}
{{/*                      type: string*/}}
{{/*                      pattern: ^[0-9]+[mMgG]?$*/}}
{{/*                    gcLoggingEnabled:*/}}
{{/*                      description: >-*/}}
{{/*                        Specifies whether the Garbage Collection logging is*/}}
{{/*                        enabled. The default is false.*/}}
{{/*                      type: boolean*/}}
{{/*                    javaSystemProperties:*/}}
{{/*                      description: >-*/}}
{{/*                        A map of additional system properties which will be*/}}
{{/*                        passed using the `-D` option to the JVM.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        properties:*/}}
{{/*                          name:*/}}
{{/*                            description: The system property name.*/}}
{{/*                            type: string*/}}
{{/*                          value:*/}}
{{/*                            description: The system property value.*/}}
{{/*                            type: string*/}}
{{/*                livenessProbe:*/}}
{{/*                  description: Pod liveness checking.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    failureThreshold:*/}}
{{/*                      description: >-*/}}
{{/*                        Minimum consecutive failures for the probe to be*/}}
{{/*                        considered failed after having succeeded. Defaults to 3.*/}}
{{/*                        Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    initialDelaySeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The initial delay before first the health is first*/}}
{{/*                        checked. Default to 15 seconds. Minimum value is 0.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 0*/}}
{{/*                    periodSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        How often (in seconds) to perform the probe. Default to*/}}
{{/*                        10 seconds. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    successThreshold:*/}}
{{/*                      description: >-*/}}
{{/*                        Minimum consecutive successes for the probe to be*/}}
{{/*                        considered successful after having failed. Defaults to*/}}
{{/*                        1. Must be 1 for liveness. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    timeoutSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The timeout for each attempted health check. Default to*/}}
{{/*                        5 seconds. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                logging:*/}}
{{/*                  description: Logging configuration for Kafka Connect.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - type*/}}
{{/*                  properties:*/}}
{{/*                    loggers:*/}}
{{/*                      description: A Map from logger name to logger level.*/}}
{{/*                      type: object*/}}
{{/*                      additionalProperties:*/}}
{{/*                        type: string*/}}
{{/*                    type:*/}}
{{/*                      description: Logging type, must be either 'inline' or 'external'.*/}}
{{/*                      type: string*/}}
{{/*                      enum:*/}}
{{/*                        - inline*/}}
{{/*                        - external*/}}
{{/*                    valueFrom:*/}}
{{/*                      description: >-*/}}
{{/*                        `ConfigMap` entry where the logging configuration is*/}}
{{/*                        stored.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        configMapKeyRef:*/}}
{{/*                          description: >-*/}}
{{/*                            Reference to the key in the ConfigMap containing the*/}}
{{/*                            configuration.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            key:*/}}
{{/*                              type: string*/}}
{{/*                            name:*/}}
{{/*                              type: string*/}}
{{/*                            optional:*/}}
{{/*                              type: boolean*/}}
{{/*                metricsConfig:*/}}
{{/*                  description: Metrics configuration.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - type*/}}
{{/*                    - valueFrom*/}}
{{/*                  properties:*/}}
{{/*                    type:*/}}
{{/*                      description: >-*/}}
{{/*                        Metrics type. Only 'jmxPrometheusExporter' supported*/}}
{{/*                        currently.*/}}
{{/*                      type: string*/}}
{{/*                      enum:*/}}
{{/*                        - jmxPrometheusExporter*/}}
{{/*                    valueFrom:*/}}
{{/*                      description: >-*/}}
{{/*                        ConfigMap entry where the Prometheus JMX Exporter*/}}
{{/*                        configuration is stored.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        configMapKeyRef:*/}}
{{/*                          description: >-*/}}
{{/*                            Reference to the key in the ConfigMap containing the*/}}
{{/*                            configuration.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            key:*/}}
{{/*                              type: string*/}}
{{/*                            name:*/}}
{{/*                              type: string*/}}
{{/*                            optional:*/}}
{{/*                              type: boolean*/}}
{{/*                rack:*/}}
{{/*                  description: >-*/}}
{{/*                    Configuration of the node label which will be used as the*/}}
{{/*                    `client.rack` consumer configuration.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - topologyKey*/}}
{{/*                  properties:*/}}
{{/*                    topologyKey:*/}}
{{/*                      description: >-*/}}
{{/*                        A key that matches labels assigned to the Kubernetes*/}}
{{/*                        cluster nodes. The value of the label is used to set a*/}}
{{/*                        broker's `broker.rack` config, and the `client.rack`*/}}
{{/*                        config for Kafka Connect or MirrorMaker 2.*/}}
{{/*                      type: string*/}}
{{/*                      example: topology.kubernetes.io/zone*/}}
{{/*                readinessProbe:*/}}
{{/*                  description: Pod readiness checking.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    failureThreshold:*/}}
{{/*                      description: >-*/}}
{{/*                        Minimum consecutive failures for the probe to be*/}}
{{/*                        considered failed after having succeeded. Defaults to 3.*/}}
{{/*                        Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    initialDelaySeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The initial delay before first the health is first*/}}
{{/*                        checked. Default to 15 seconds. Minimum value is 0.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 0*/}}
{{/*                    periodSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        How often (in seconds) to perform the probe. Default to*/}}
{{/*                        10 seconds. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    successThreshold:*/}}
{{/*                      description: >-*/}}
{{/*                        Minimum consecutive successes for the probe to be*/}}
{{/*                        considered successful after having failed. Defaults to*/}}
{{/*                        1. Must be 1 for liveness. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                    timeoutSeconds:*/}}
{{/*                      description: >-*/}}
{{/*                        The timeout for each attempted health check. Default to*/}}
{{/*                        5 seconds. Minimum value is 1.*/}}
{{/*                      type: integer*/}}
{{/*                      minimum: 1*/}}
{{/*                replicas:*/}}
{{/*                  description: >-*/}}
{{/*                    The number of pods in the Kafka Connect group. Defaults to*/}}
{{/*                    `3`.*/}}
{{/*                  type: integer*/}}
{{/*                resources:*/}}
{{/*                  description: >-*/}}
{{/*                    The maximum limits for CPU and memory resources and the*/}}
{{/*                    requested initial resources.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    claims:*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        properties:*/}}
{{/*                          name:*/}}
{{/*                            type: string*/}}
{{/*                    limits:*/}}
{{/*                      type: object*/}}
{{/*                      additionalProperties:*/}}
{{/*                        pattern: >-*/}}
{{/*                          ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$*/}}
{{/*                        anyOf:*/}}
{{/*                          - type: integer*/}}
{{/*                          - type: string*/}}
{{/*                        x-kubernetes-int-or-string: true*/}}
{{/*                    requests:*/}}
{{/*                      type: object*/}}
{{/*                      additionalProperties:*/}}
{{/*                        pattern: >-*/}}
{{/*                          ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$*/}}
{{/*                        anyOf:*/}}
{{/*                          - type: integer*/}}
{{/*                          - type: string*/}}
{{/*                        x-kubernetes-int-or-string: true*/}}
{{/*                template:*/}}
{{/*                  description: >-*/}}
{{/*                    Template for Kafka Connect and Kafka Mirror Maker 2*/}}
{{/*                    resources. The template allows users to specify how the*/}}
{{/*                    `Pods`, `Service`, and other services are generated.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    apiService:*/}}
{{/*                      description: Template for Kafka Connect API `Service`.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        ipFamilies:*/}}
{{/*                          description: >-*/}}
{{/*                            Specifies the IP Families used by the service.*/}}
{{/*                            Available options are `IPv4` and `IPv6`. If*/}}
{{/*                            unspecified, Kubernetes will choose the default*/}}
{{/*                            value based on the `ipFamilyPolicy` setting.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: string*/}}
{{/*                            enum:*/}}
{{/*                              - IPv4*/}}
{{/*                              - IPv6*/}}
{{/*                        ipFamilyPolicy:*/}}
{{/*                          description: >-*/}}
{{/*                            Specifies the IP Family Policy used by the service.*/}}
{{/*                            Available options are `SingleStack`,*/}}
{{/*                            `PreferDualStack` and `RequireDualStack`.*/}}
{{/*                            `SingleStack` is for a single IP family.*/}}
{{/*                            `PreferDualStack` is for two IP families on*/}}
{{/*                            dual-stack configured clusters or a single IP family*/}}
{{/*                            on single-stack clusters. `RequireDualStack` fails*/}}
{{/*                            unless there are two IP families on dual-stack*/}}
{{/*                            configured clusters. If unspecified, Kubernetes will*/}}
{{/*                            choose the default value based on the service type.*/}}
{{/*                          type: string*/}}
{{/*                          enum:*/}}
{{/*                            - SingleStack*/}}
{{/*                            - PreferDualStack*/}}
{{/*                            - RequireDualStack*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    buildConfig:*/}}
{{/*                      description: >-*/}}
{{/*                        Template for the Kafka Connect BuildConfig used to build*/}}
{{/*                        new container images. The BuildConfig is used only on*/}}
{{/*                        OpenShift.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: >-*/}}
{{/*                            Metadata to apply to the*/}}
{{/*                            `PodDisruptionBudgetTemplate` resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                        pullSecret:*/}}
{{/*                          description: >-*/}}
{{/*                            Container Registry Secret with the credentials for*/}}
{{/*                            pulling the base image.*/}}
{{/*                          type: string*/}}
{{/*                    buildContainer:*/}}
{{/*                      description: >-*/}}
{{/*                        Template for the Kafka Connect Build container. The*/}}
{{/*                        build container is used only on Kubernetes.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        env:*/}}
{{/*                          description: >-*/}}
{{/*                            Environment variables which should be applied to the*/}}
{{/*                            container.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                description: The environment variable key.*/}}
{{/*                                type: string*/}}
{{/*                              value:*/}}
{{/*                                description: The environment variable value.*/}}
{{/*                                type: string*/}}
{{/*                        securityContext:*/}}
{{/*                          description: Security context for the container.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            allowPrivilegeEscalation:*/}}
{{/*                              type: boolean*/}}
{{/*                            appArmorProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            capabilities:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                add:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                                drop:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                            privileged:*/}}
{{/*                              type: boolean*/}}
{{/*                            procMount:*/}}
{{/*                              type: string*/}}
{{/*                            readOnlyRootFilesystem:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            runAsNonRoot:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsUser:*/}}
{{/*                              type: integer*/}}
{{/*                            seLinuxOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                level:*/}}
{{/*                                  type: string*/}}
{{/*                                role:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                                user:*/}}
{{/*                                  type: string*/}}
{{/*                            seccompProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            windowsOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                gmsaCredentialSpec:*/}}
{{/*                                  type: string*/}}
{{/*                                gmsaCredentialSpecName:*/}}
{{/*                                  type: string*/}}
{{/*                                hostProcess:*/}}
{{/*                                  type: boolean*/}}
{{/*                                runAsUserName:*/}}
{{/*                                  type: string*/}}
{{/*                    buildPod:*/}}
{{/*                      description: >-*/}}
{{/*                        Template for Kafka Connect Build `Pods`. The build pod*/}}
{{/*                        is used only on Kubernetes.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        affinity:*/}}
{{/*                          description: The pod's affinity rules.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            nodeAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      preference:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchFields:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: object*/}}
{{/*                                  properties:*/}}
{{/*                                    nodeSelectorTerms:*/}}
{{/*                                      type: array*/}}
{{/*                                      items:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchFields:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                            podAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      podAffinityTerm:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          labelSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          matchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          mismatchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          namespaceSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          namespaces:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          topologyKey:*/}}
{{/*                                            type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      labelSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      matchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      mismatchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      namespaceSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      namespaces:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      topologyKey:*/}}
{{/*                                        type: string*/}}
{{/*                            podAntiAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      podAffinityTerm:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          labelSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          matchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          mismatchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          namespaceSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          namespaces:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          topologyKey:*/}}
{{/*                                            type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      labelSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      matchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      mismatchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      namespaceSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      namespaces:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      topologyKey:*/}}
{{/*                                        type: string*/}}
{{/*                        enableServiceLinks:*/}}
{{/*                          description: >-*/}}
{{/*                            Indicates whether information about services should*/}}
{{/*                            be injected into Pod's environment variables.*/}}
{{/*                          type: boolean*/}}
{{/*                        hostAliases:*/}}
{{/*                          description: >-*/}}
{{/*                            The pod's HostAliases. HostAliases is an optional*/}}
{{/*                            list of hosts and IPs that will be injected into the*/}}
{{/*                            Pod's hosts file if specified.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              hostnames:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: string*/}}
{{/*                              ip:*/}}
{{/*                                type: string*/}}
{{/*                        imagePullSecrets:*/}}
{{/*                          description: >-*/}}
{{/*                            List of references to secrets in the same namespace*/}}
{{/*                            to use for pulling any of the images used by this*/}}
{{/*                            Pod. When the `STRIMZI_IMAGE_PULL_SECRETS`*/}}
{{/*                            environment variable in Cluster Operator and the*/}}
{{/*                            `imagePullSecrets` option are specified, only the*/}}
{{/*                            `imagePullSecrets` variable is used and the*/}}
{{/*                            `STRIMZI_IMAGE_PULL_SECRETS` variable is ignored.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                type: string*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                        priorityClassName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the priority class used to assign*/}}
{{/*                            priority to the pods.*/}}
{{/*                          type: string*/}}
{{/*                        schedulerName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the scheduler used to dispatch this*/}}
{{/*                            `Pod`. If not specified, the default scheduler will*/}}
{{/*                            be used.*/}}
{{/*                          type: string*/}}
{{/*                        securityContext:*/}}
{{/*                          description: >-*/}}
{{/*                            Configures pod-level security attributes and common*/}}
{{/*                            container settings.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            appArmorProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            fsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            fsGroupChangePolicy:*/}}
{{/*                              type: string*/}}
{{/*                            runAsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            runAsNonRoot:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsUser:*/}}
{{/*                              type: integer*/}}
{{/*                            seLinuxOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                level:*/}}
{{/*                                  type: string*/}}
{{/*                                role:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                                user:*/}}
{{/*                                  type: string*/}}
{{/*                            seccompProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            supplementalGroups:*/}}
{{/*                              type: array*/}}
{{/*                              items:*/}}
{{/*                                type: integer*/}}
{{/*                            sysctls:*/}}
{{/*                              type: array*/}}
{{/*                              items:*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  name:*/}}
{{/*                                    type: string*/}}
{{/*                                  value:*/}}
{{/*                                    type: string*/}}
{{/*                            windowsOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                gmsaCredentialSpec:*/}}
{{/*                                  type: string*/}}
{{/*                                gmsaCredentialSpecName:*/}}
{{/*                                  type: string*/}}
{{/*                                hostProcess:*/}}
{{/*                                  type: boolean*/}}
{{/*                                runAsUserName:*/}}
{{/*                                  type: string*/}}
{{/*                        terminationGracePeriodSeconds:*/}}
{{/*                          description: >-*/}}
{{/*                            The grace period is the duration in seconds after*/}}
{{/*                            the processes running in the pod are sent a*/}}
{{/*                            termination signal, and the time when the processes*/}}
{{/*                            are forcibly halted with a kill signal. Set this*/}}
{{/*                            value to longer than the expected cleanup time for*/}}
{{/*                            your process. Value must be a non-negative integer.*/}}
{{/*                            A zero value indicates delete immediately. You might*/}}
{{/*                            need to increase the grace period for very large*/}}
{{/*                            Kafka clusters, so that the Kafka brokers have*/}}
{{/*                            enough time to transfer their work to another broker*/}}
{{/*                            before they are terminated. Defaults to 30 seconds.*/}}
{{/*                          type: integer*/}}
{{/*                          minimum: 0*/}}
{{/*                        tmpDirSizeLimit:*/}}
{{/*                          description: >-*/}}
{{/*                            Defines the total amount (for example `1Gi`) of*/}}
{{/*                            local storage required for temporary EmptyDir volume*/}}
{{/*                            (`/tmp`). Default value is `5Mi`.*/}}
{{/*                          type: string*/}}
{{/*                          pattern: ^([0-9.]+)([eEinumkKMGTP]*[-+]?[0-9]*)$*/}}
{{/*                        tolerations:*/}}
{{/*                          description: The pod's tolerations.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              effect:*/}}
{{/*                                type: string*/}}
{{/*                              key:*/}}
{{/*                                type: string*/}}
{{/*                              operator:*/}}
{{/*                                type: string*/}}
{{/*                              tolerationSeconds:*/}}
{{/*                                type: integer*/}}
{{/*                              value:*/}}
{{/*                                type: string*/}}
{{/*                        topologySpreadConstraints:*/}}
{{/*                          description: The pod's topology spread constraints.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              labelSelector:*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  matchExpressions:*/}}
{{/*                                    type: array*/}}
{{/*                                    items:*/}}
{{/*                                      type: object*/}}
{{/*                                      properties:*/}}
{{/*                                        key:*/}}
{{/*                                          type: string*/}}
{{/*                                        operator:*/}}
{{/*                                          type: string*/}}
{{/*                                        values:*/}}
{{/*                                          type: array*/}}
{{/*                                          items:*/}}
{{/*                                            type: string*/}}
{{/*                                  matchLabels:*/}}
{{/*                                    type: object*/}}
{{/*                                    additionalProperties:*/}}
{{/*                                      type: string*/}}
{{/*                              matchLabelKeys:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: string*/}}
{{/*                              maxSkew:*/}}
{{/*                                type: integer*/}}
{{/*                              minDomains:*/}}
{{/*                                type: integer*/}}
{{/*                              nodeAffinityPolicy:*/}}
{{/*                                type: string*/}}
{{/*                              nodeTaintsPolicy:*/}}
{{/*                                type: string*/}}
{{/*                              topologyKey:*/}}
{{/*                                type: string*/}}
{{/*                              whenUnsatisfiable:*/}}
{{/*                                type: string*/}}
{{/*                    buildServiceAccount:*/}}
{{/*                      description: Template for the Kafka Connect Build service account.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    clusterRoleBinding:*/}}
{{/*                      description: Template for the Kafka Connect ClusterRoleBinding.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    connectContainer:*/}}
{{/*                      description: Template for the Kafka Connect container.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        env:*/}}
{{/*                          description: >-*/}}
{{/*                            Environment variables which should be applied to the*/}}
{{/*                            container.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                description: The environment variable key.*/}}
{{/*                                type: string*/}}
{{/*                              value:*/}}
{{/*                                description: The environment variable value.*/}}
{{/*                                type: string*/}}
{{/*                        securityContext:*/}}
{{/*                          description: Security context for the container.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            allowPrivilegeEscalation:*/}}
{{/*                              type: boolean*/}}
{{/*                            appArmorProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            capabilities:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                add:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                                drop:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                            privileged:*/}}
{{/*                              type: boolean*/}}
{{/*                            procMount:*/}}
{{/*                              type: string*/}}
{{/*                            readOnlyRootFilesystem:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            runAsNonRoot:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsUser:*/}}
{{/*                              type: integer*/}}
{{/*                            seLinuxOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                level:*/}}
{{/*                                  type: string*/}}
{{/*                                role:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                                user:*/}}
{{/*                                  type: string*/}}
{{/*                            seccompProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            windowsOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                gmsaCredentialSpec:*/}}
{{/*                                  type: string*/}}
{{/*                                gmsaCredentialSpecName:*/}}
{{/*                                  type: string*/}}
{{/*                                hostProcess:*/}}
{{/*                                  type: boolean*/}}
{{/*                                runAsUserName:*/}}
{{/*                                  type: string*/}}
{{/*                    deployment:*/}}
{{/*                      description: Template for Kafka Connect `Deployment`.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        deploymentStrategy:*/}}
{{/*                          description: >-*/}}
{{/*                            Pod replacement strategy for deployment*/}}
{{/*                            configuration changes. Valid values are*/}}
{{/*                            `RollingUpdate` and `Recreate`. Defaults to*/}}
{{/*                            `RollingUpdate`.*/}}
{{/*                          type: string*/}}
{{/*                          enum:*/}}
{{/*                            - RollingUpdate*/}}
{{/*                            - Recreate*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    headlessService:*/}}
{{/*                      description: Template for Kafka Connect headless `Service`.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        ipFamilies:*/}}
{{/*                          description: >-*/}}
{{/*                            Specifies the IP Families used by the service.*/}}
{{/*                            Available options are `IPv4` and `IPv6`. If*/}}
{{/*                            unspecified, Kubernetes will choose the default*/}}
{{/*                            value based on the `ipFamilyPolicy` setting.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: string*/}}
{{/*                            enum:*/}}
{{/*                              - IPv4*/}}
{{/*                              - IPv6*/}}
{{/*                        ipFamilyPolicy:*/}}
{{/*                          description: >-*/}}
{{/*                            Specifies the IP Family Policy used by the service.*/}}
{{/*                            Available options are `SingleStack`,*/}}
{{/*                            `PreferDualStack` and `RequireDualStack`.*/}}
{{/*                            `SingleStack` is for a single IP family.*/}}
{{/*                            `PreferDualStack` is for two IP families on*/}}
{{/*                            dual-stack configured clusters or a single IP family*/}}
{{/*                            on single-stack clusters. `RequireDualStack` fails*/}}
{{/*                            unless there are two IP families on dual-stack*/}}
{{/*                            configured clusters. If unspecified, Kubernetes will*/}}
{{/*                            choose the default value based on the service type.*/}}
{{/*                          type: string*/}}
{{/*                          enum:*/}}
{{/*                            - SingleStack*/}}
{{/*                            - PreferDualStack*/}}
{{/*                            - RequireDualStack*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    initContainer:*/}}
{{/*                      description: Template for the Kafka init container.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        env:*/}}
{{/*                          description: >-*/}}
{{/*                            Environment variables which should be applied to the*/}}
{{/*                            container.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                description: The environment variable key.*/}}
{{/*                                type: string*/}}
{{/*                              value:*/}}
{{/*                                description: The environment variable value.*/}}
{{/*                                type: string*/}}
{{/*                        securityContext:*/}}
{{/*                          description: Security context for the container.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            allowPrivilegeEscalation:*/}}
{{/*                              type: boolean*/}}
{{/*                            appArmorProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            capabilities:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                add:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                                drop:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: string*/}}
{{/*                            privileged:*/}}
{{/*                              type: boolean*/}}
{{/*                            procMount:*/}}
{{/*                              type: string*/}}
{{/*                            readOnlyRootFilesystem:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            runAsNonRoot:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsUser:*/}}
{{/*                              type: integer*/}}
{{/*                            seLinuxOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                level:*/}}
{{/*                                  type: string*/}}
{{/*                                role:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                                user:*/}}
{{/*                                  type: string*/}}
{{/*                            seccompProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            windowsOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                gmsaCredentialSpec:*/}}
{{/*                                  type: string*/}}
{{/*                                gmsaCredentialSpecName:*/}}
{{/*                                  type: string*/}}
{{/*                                hostProcess:*/}}
{{/*                                  type: boolean*/}}
{{/*                                runAsUserName:*/}}
{{/*                                  type: string*/}}
{{/*                    jmxSecret:*/}}
{{/*                      description: >-*/}}
{{/*                        Template for Secret of the Kafka Connect Cluster JMX*/}}
{{/*                        authentication.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    pod:*/}}
{{/*                      description: Template for Kafka Connect `Pods`.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        affinity:*/}}
{{/*                          description: The pod's affinity rules.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            nodeAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      preference:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchFields:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: object*/}}
{{/*                                  properties:*/}}
{{/*                                    nodeSelectorTerms:*/}}
{{/*                                      type: array*/}}
{{/*                                      items:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchFields:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                            podAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      podAffinityTerm:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          labelSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          matchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          mismatchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          namespaceSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          namespaces:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          topologyKey:*/}}
{{/*                                            type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      labelSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      matchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      mismatchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      namespaceSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      namespaces:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      topologyKey:*/}}
{{/*                                        type: string*/}}
{{/*                            podAntiAffinity:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                preferredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      podAffinityTerm:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          labelSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          matchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          mismatchLabelKeys:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          namespaceSelector:*/}}
{{/*                                            type: object*/}}
{{/*                                            properties:*/}}
{{/*                                              matchExpressions:*/}}
{{/*                                                type: array*/}}
{{/*                                                items:*/}}
{{/*                                                  type: object*/}}
{{/*                                                  properties:*/}}
{{/*                                                    key:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    operator:*/}}
{{/*                                                      type: string*/}}
{{/*                                                    values:*/}}
{{/*                                                      type: array*/}}
{{/*                                                      items:*/}}
{{/*                                                        type: string*/}}
{{/*                                              matchLabels:*/}}
{{/*                                                type: object*/}}
{{/*                                                additionalProperties:*/}}
{{/*                                                  type: string*/}}
{{/*                                          namespaces:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: string*/}}
{{/*                                          topologyKey:*/}}
{{/*                                            type: string*/}}
{{/*                                      weight:*/}}
{{/*                                        type: integer*/}}
{{/*                                requiredDuringSchedulingIgnoredDuringExecution:*/}}
{{/*                                  type: array*/}}
{{/*                                  items:*/}}
{{/*                                    type: object*/}}
{{/*                                    properties:*/}}
{{/*                                      labelSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      matchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      mismatchLabelKeys:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      namespaceSelector:*/}}
{{/*                                        type: object*/}}
{{/*                                        properties:*/}}
{{/*                                          matchExpressions:*/}}
{{/*                                            type: array*/}}
{{/*                                            items:*/}}
{{/*                                              type: object*/}}
{{/*                                              properties:*/}}
{{/*                                                key:*/}}
{{/*                                                  type: string*/}}
{{/*                                                operator:*/}}
{{/*                                                  type: string*/}}
{{/*                                                values:*/}}
{{/*                                                  type: array*/}}
{{/*                                                  items:*/}}
{{/*                                                    type: string*/}}
{{/*                                          matchLabels:*/}}
{{/*                                            type: object*/}}
{{/*                                            additionalProperties:*/}}
{{/*                                              type: string*/}}
{{/*                                      namespaces:*/}}
{{/*                                        type: array*/}}
{{/*                                        items:*/}}
{{/*                                          type: string*/}}
{{/*                                      topologyKey:*/}}
{{/*                                        type: string*/}}
{{/*                        enableServiceLinks:*/}}
{{/*                          description: >-*/}}
{{/*                            Indicates whether information about services should*/}}
{{/*                            be injected into Pod's environment variables.*/}}
{{/*                          type: boolean*/}}
{{/*                        hostAliases:*/}}
{{/*                          description: >-*/}}
{{/*                            The pod's HostAliases. HostAliases is an optional*/}}
{{/*                            list of hosts and IPs that will be injected into the*/}}
{{/*                            Pod's hosts file if specified.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              hostnames:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: string*/}}
{{/*                              ip:*/}}
{{/*                                type: string*/}}
{{/*                        imagePullSecrets:*/}}
{{/*                          description: >-*/}}
{{/*                            List of references to secrets in the same namespace*/}}
{{/*                            to use for pulling any of the images used by this*/}}
{{/*                            Pod. When the `STRIMZI_IMAGE_PULL_SECRETS`*/}}
{{/*                            environment variable in Cluster Operator and the*/}}
{{/*                            `imagePullSecrets` option are specified, only the*/}}
{{/*                            `imagePullSecrets` variable is used and the*/}}
{{/*                            `STRIMZI_IMAGE_PULL_SECRETS` variable is ignored.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              name:*/}}
{{/*                                type: string*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                        priorityClassName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the priority class used to assign*/}}
{{/*                            priority to the pods.*/}}
{{/*                          type: string*/}}
{{/*                        schedulerName:*/}}
{{/*                          description: >-*/}}
{{/*                            The name of the scheduler used to dispatch this*/}}
{{/*                            `Pod`. If not specified, the default scheduler will*/}}
{{/*                            be used.*/}}
{{/*                          type: string*/}}
{{/*                        securityContext:*/}}
{{/*                          description: >-*/}}
{{/*                            Configures pod-level security attributes and common*/}}
{{/*                            container settings.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            appArmorProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            fsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            fsGroupChangePolicy:*/}}
{{/*                              type: string*/}}
{{/*                            runAsGroup:*/}}
{{/*                              type: integer*/}}
{{/*                            runAsNonRoot:*/}}
{{/*                              type: boolean*/}}
{{/*                            runAsUser:*/}}
{{/*                              type: integer*/}}
{{/*                            seLinuxOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                level:*/}}
{{/*                                  type: string*/}}
{{/*                                role:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                                user:*/}}
{{/*                                  type: string*/}}
{{/*                            seccompProfile:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                localhostProfile:*/}}
{{/*                                  type: string*/}}
{{/*                                type:*/}}
{{/*                                  type: string*/}}
{{/*                            supplementalGroups:*/}}
{{/*                              type: array*/}}
{{/*                              items:*/}}
{{/*                                type: integer*/}}
{{/*                            sysctls:*/}}
{{/*                              type: array*/}}
{{/*                              items:*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  name:*/}}
{{/*                                    type: string*/}}
{{/*                                  value:*/}}
{{/*                                    type: string*/}}
{{/*                            windowsOptions:*/}}
{{/*                              type: object*/}}
{{/*                              properties:*/}}
{{/*                                gmsaCredentialSpec:*/}}
{{/*                                  type: string*/}}
{{/*                                gmsaCredentialSpecName:*/}}
{{/*                                  type: string*/}}
{{/*                                hostProcess:*/}}
{{/*                                  type: boolean*/}}
{{/*                                runAsUserName:*/}}
{{/*                                  type: string*/}}
{{/*                        terminationGracePeriodSeconds:*/}}
{{/*                          description: >-*/}}
{{/*                            The grace period is the duration in seconds after*/}}
{{/*                            the processes running in the pod are sent a*/}}
{{/*                            termination signal, and the time when the processes*/}}
{{/*                            are forcibly halted with a kill signal. Set this*/}}
{{/*                            value to longer than the expected cleanup time for*/}}
{{/*                            your process. Value must be a non-negative integer.*/}}
{{/*                            A zero value indicates delete immediately. You might*/}}
{{/*                            need to increase the grace period for very large*/}}
{{/*                            Kafka clusters, so that the Kafka brokers have*/}}
{{/*                            enough time to transfer their work to another broker*/}}
{{/*                            before they are terminated. Defaults to 30 seconds.*/}}
{{/*                          type: integer*/}}
{{/*                          minimum: 0*/}}
{{/*                        tmpDirSizeLimit:*/}}
{{/*                          description: >-*/}}
{{/*                            Defines the total amount (for example `1Gi`) of*/}}
{{/*                            local storage required for temporary EmptyDir volume*/}}
{{/*                            (`/tmp`). Default value is `5Mi`.*/}}
{{/*                          type: string*/}}
{{/*                          pattern: ^([0-9.]+)([eEinumkKMGTP]*[-+]?[0-9]*)$*/}}
{{/*                        tolerations:*/}}
{{/*                          description: The pod's tolerations.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              effect:*/}}
{{/*                                type: string*/}}
{{/*                              key:*/}}
{{/*                                type: string*/}}
{{/*                              operator:*/}}
{{/*                                type: string*/}}
{{/*                              tolerationSeconds:*/}}
{{/*                                type: integer*/}}
{{/*                              value:*/}}
{{/*                                type: string*/}}
{{/*                        topologySpreadConstraints:*/}}
{{/*                          description: The pod's topology spread constraints.*/}}
{{/*                          type: array*/}}
{{/*                          items:*/}}
{{/*                            type: object*/}}
{{/*                            properties:*/}}
{{/*                              labelSelector:*/}}
{{/*                                type: object*/}}
{{/*                                properties:*/}}
{{/*                                  matchExpressions:*/}}
{{/*                                    type: array*/}}
{{/*                                    items:*/}}
{{/*                                      type: object*/}}
{{/*                                      properties:*/}}
{{/*                                        key:*/}}
{{/*                                          type: string*/}}
{{/*                                        operator:*/}}
{{/*                                          type: string*/}}
{{/*                                        values:*/}}
{{/*                                          type: array*/}}
{{/*                                          items:*/}}
{{/*                                            type: string*/}}
{{/*                                  matchLabels:*/}}
{{/*                                    type: object*/}}
{{/*                                    additionalProperties:*/}}
{{/*                                      type: string*/}}
{{/*                              matchLabelKeys:*/}}
{{/*                                type: array*/}}
{{/*                                items:*/}}
{{/*                                  type: string*/}}
{{/*                              maxSkew:*/}}
{{/*                                type: integer*/}}
{{/*                              minDomains:*/}}
{{/*                                type: integer*/}}
{{/*                              nodeAffinityPolicy:*/}}
{{/*                                type: string*/}}
{{/*                              nodeTaintsPolicy:*/}}
{{/*                                type: string*/}}
{{/*                              topologyKey:*/}}
{{/*                                type: string*/}}
{{/*                              whenUnsatisfiable:*/}}
{{/*                                type: string*/}}
{{/*                    podDisruptionBudget:*/}}
{{/*                      description: Template for Kafka Connect `PodDisruptionBudget`.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        maxUnavailable:*/}}
{{/*                          description: >-*/}}
{{/*                            Maximum number of unavailable pods to allow*/}}
{{/*                            automatic Pod eviction. A Pod eviction is allowed*/}}
{{/*                            when the `maxUnavailable` number of pods or fewer*/}}
{{/*                            are unavailable after the eviction. Setting this*/}}
{{/*                            value to 0 prevents all voluntary evictions, so the*/}}
{{/*                            pods must be evicted manually. Defaults to 1.*/}}
{{/*                          type: integer*/}}
{{/*                          minimum: 0*/}}
{{/*                        metadata:*/}}
{{/*                          description: >-*/}}
{{/*                            Metadata to apply to the*/}}
{{/*                            `PodDisruptionBudgetTemplate` resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    podSet:*/}}
{{/*                      description: Template for Kafka Connect `StrimziPodSet` resource.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                    serviceAccount:*/}}
{{/*                      description: Template for the Kafka Connect service account.*/}}
{{/*                      type: object*/}}
{{/*                      properties:*/}}
{{/*                        metadata:*/}}
{{/*                          description: Metadata applied to the resource.*/}}
{{/*                          type: object*/}}
{{/*                          properties:*/}}
{{/*                            annotations:*/}}
{{/*                              description: Annotations added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                            labels:*/}}
{{/*                              description: Labels added to the Kubernetes resource.*/}}
{{/*                              type: object*/}}
{{/*                              additionalProperties:*/}}
{{/*                                type: string*/}}
{{/*                tls:*/}}
{{/*                  description: TLS configuration.*/}}
{{/*                  type: object*/}}
{{/*                  properties:*/}}
{{/*                    trustedCertificates:*/}}
{{/*                      description: Trusted certificates for TLS connection.*/}}
{{/*                      type: array*/}}
{{/*                      items:*/}}
{{/*                        type: object*/}}
{{/*                        required:*/}}
{{/*                          - secretName*/}}
{{/*                        oneOf:*/}}
{{/*                          - required:*/}}
{{/*                              - certificate*/}}
{{/*                            properties:*/}}
{{/*                              certificate: {}*/}}
{{/*                          - required:*/}}
{{/*                              - pattern*/}}
{{/*                            properties:*/}}
{{/*                              pattern: {}*/}}
{{/*                        properties:*/}}
{{/*                          certificate:*/}}
{{/*                            description: The name of the file certificate in the secret.*/}}
{{/*                            type: string*/}}
{{/*                          pattern:*/}}
{{/*                            description: >-*/}}
{{/*                              Pattern for the certificate files in the secret.*/}}
{{/*                              Use the*/}}
{{/*                              link:https://en.wikipedia.org/wiki/Glob_(programming)[_glob*/}}
{{/*                              syntax_] for the pattern. All files in the secret*/}}
{{/*                              that match the pattern are used.*/}}
{{/*                            type: string*/}}
{{/*                          secretName:*/}}
{{/*                            description: The name of the Secret containing the certificate.*/}}
{{/*                            type: string*/}}
{{/*                tracing:*/}}
{{/*                  description: The configuration of tracing in Kafka Connect.*/}}
{{/*                  type: object*/}}
{{/*                  required:*/}}
{{/*                    - type*/}}
{{/*                  properties:*/}}
{{/*                    type:*/}}
{{/*                      description: >-*/}}
{{/*                        Type of the tracing used. Currently the only supported*/}}
{{/*                        type is `opentelemetry` for OpenTelemetry tracing. As of*/}}
{{/*                        Strimzi 0.37.0, `jaeger` type is not supported anymore*/}}
{{/*                        and this option is ignored.*/}}
{{/*                      type: string*/}}
{{/*                      enum:*/}}
{{/*                        - jaeger*/}}
{{/*                        - opentelemetry*/}}
{{/*                version:*/}}
{{/*                  description: >-*/}}
{{/*                    The Kafka Connect version. Defaults to the latest version.*/}}
{{/*                    Consult the user documentation to understand the process*/}}
{{/*                    required to upgrade or downgrade the version.*/}}
{{/*                  type: string*/}}
{{/*            status:*/}}
{{/*              description: The status of the Kafka Connect cluster.*/}}
{{/*              type: object*/}}
{{/*              properties:*/}}
{{/*                conditions:*/}}
{{/*                  description: List of status conditions.*/}}
{{/*                  type: array*/}}
{{/*                  items:*/}}
{{/*                    type: object*/}}
{{/*                    properties:*/}}
{{/*                      lastTransitionTime:*/}}
{{/*                        description: >-*/}}
{{/*                          Last time the condition of a type changed from one*/}}
{{/*                          status to another. The required format is*/}}
{{/*                          'yyyy-MM-ddTHH:mm:ssZ', in the UTC time zone.*/}}
{{/*                        type: string*/}}
{{/*                      message:*/}}
{{/*                        description: >-*/}}
{{/*                          Human-readable message indicating details about the*/}}
{{/*                          condition's last transition.*/}}
{{/*                        type: string*/}}
{{/*                      reason:*/}}
{{/*                        description: >-*/}}
{{/*                          The reason for the condition's last transition (a*/}}
{{/*                          single word in CamelCase).*/}}
{{/*                        type: string*/}}
{{/*                      status:*/}}
{{/*                        description: >-*/}}
{{/*                          The status of the condition, either True, False or*/}}
{{/*                          Unknown.*/}}
{{/*                        type: string*/}}
{{/*                      type:*/}}
{{/*                        description: >-*/}}
{{/*                          The unique identifier of a condition, used to*/}}
{{/*                          distinguish between other conditions in the resource.*/}}
{{/*                        type: string*/}}
{{/*                connectorPlugins:*/}}
{{/*                  description: >-*/}}
{{/*                    The list of connector plugins available in this Kafka*/}}
{{/*                    Connect deployment.*/}}
{{/*                  type: array*/}}
{{/*                  items:*/}}
{{/*                    type: object*/}}
{{/*                    properties:*/}}
{{/*                      class:*/}}
{{/*                        description: The class of the connector plugin.*/}}
{{/*                        type: string*/}}
{{/*                      type:*/}}
{{/*                        description: >-*/}}
{{/*                          The type of the connector plugin. The available types*/}}
{{/*                          are `sink` and `source`.*/}}
{{/*                        type: string*/}}
{{/*                      version:*/}}
{{/*                        description: The version of the connector plugin.*/}}
{{/*                        type: string*/}}
{{/*                labelSelector:*/}}
{{/*                  description: Label selector for pods providing this resource.*/}}
{{/*                  type: string*/}}
{{/*                observedGeneration:*/}}
{{/*                  description: >-*/}}
{{/*                    The generation of the CRD that was last reconciled by the*/}}
{{/*                    operator.*/}}
{{/*                  type: integer*/}}
{{/*                replicas:*/}}
{{/*                  description: >-*/}}
{{/*                    The current number of pods being used to provide this*/}}
{{/*                    resource.*/}}
{{/*                  type: integer*/}}
{{/*                url:*/}}
{{/*                  description: >-*/}}
{{/*                    The URL of the REST API endpoint for managing and monitoring*/}}
{{/*                    Kafka Connect connectors.*/}}
{{/*                  type: string*/}}
{{/*      subresources:*/}}
{{/*        status: {}*/}}
{{/*        scale:*/}}
{{/*          specReplicasPath: .spec.replicas*/}}
{{/*          statusReplicasPath: .status.replicas*/}}
{{/*          labelSelectorPath: .status.labelSelector*/}}
{{/*      additionalPrinterColumns:*/}}
{{/*        - name: Desired replicas*/}}
{{/*          type: integer*/}}
{{/*          description: The desired number of Kafka Connect replicas*/}}
{{/*          jsonPath: .spec.replicas*/}}
{{/*        - name: Ready*/}}
{{/*          type: string*/}}
{{/*          description: The state of the custom resource*/}}
{{/*          jsonPath: .status.conditions[?(@.type=="Ready")].status*/}}
{{/*  conversion:*/}}
{{/*    strategy: None*/}}
