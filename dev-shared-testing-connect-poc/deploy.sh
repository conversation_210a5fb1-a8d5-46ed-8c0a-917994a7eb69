#!/bin/bash -e

# Save the current working directory
pushd ./chart > /dev/null

# Check if the release exists
if helm -n kafka-testing status kafka-connect-release-testing > /dev/null 2>&1; then
  # If the release exists, upgrade it
  helm upgrade -n kafka-testing kafka-connect-release-testing ./ -f values.yaml
else
  # If the release does not exist, install it
  helm install -n kafka-testing kafka-connect-release-testing ./ -f values.yaml
fi

# Restore the original working directory
popd > /dev/null