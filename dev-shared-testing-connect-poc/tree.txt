./
├── build.sh
├── chart
│   ├── Archive.zip
│   ├── Chart.yaml
│   ├── command.sh
│   ├── strimzi-connect.properties
│   ├── templates
│   │   ├── connect-cluster.yaml
│   │   ├── pod.yaml
│   │   ├── tbl
│   │   │   └── _helpers.tpl
│   │   ├── topics.yaml
│   │   └── user.yaml
│   ├── test.sh
│   └── values.yaml
├── crd.yaml
├── deploy.sh
├── destroy.sh
├── generated.yaml
├── opt
│   └── kafka
│       └── kafka_connect_run.sh
├── template.sh
├── tmp
│   └── strimzi-connect.properties
└── tree.txt

7 directories, 20 files
