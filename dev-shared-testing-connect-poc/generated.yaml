---
# Source: connect-cluster-testing/templates/kafka-cluster.yaml
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: khaled-testing-connect-cluster
#  annotations:
#  # use-connector-resources configures this KafkaConnect
#  # to use KafkaConnector resources to avoid
#  # needing to call the Connect REST API directly
#    strimzi.io/use-connector-resources: "true"
spec:
  version: 3.7.0
  replicas: 1
  bootstrapServers: testing.kafka.shared-dev.eu-west-1.careem-tech.com:9094
#  tls:
#    trustedCertificates:
#      - secretName: my-cluster-cluster-ca-cert
#        pattern: "*.crt"
  config:
    group.id: connect-cluster-testing
    offset.storage.topic: connect-cluster-offsets-testing
    config.storage.topic: connect-cluster-configs-testing
    status.storage.topic: connect-cluster-status-testing
    # -1 means it will use the default replication factor configured in the broker
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1
