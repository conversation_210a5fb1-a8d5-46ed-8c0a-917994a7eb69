---
apiVersion: v2
name: connect-cluster-testing
description: A Helm chart for connect cluster
type: application
version: 0.1.0
appVersion: "0.1.0"
#dependencies:
#  - name: strimzi-kafka-operator
#    version: "0.42.0"
#    repository: "https://strimzi.io/charts/"
#  - name: schema-registry-operator
#    version: "0.0.1"
#    repository: file://charts/schema-registry-operator
#    condition: true
#  - name: cp-schema-registry
#    version: "0.1.0"
#    repository: file://charts/cp-schema-registry
#    condition: cp-schema-registry.enabled

#  - name: kafka-careem-integration-operator
#    version: "0.0.1"
#    repository: file://charts/kafka-careem-integration-operator
#    condition: kafka-careem-integration-operator.enabled
#  - name: kafka-careem-integration-operator-testing
#    version: "0.0.1"
#    repository: file://charts/kafka-careem-integration-operator-testing
#    condition: kafka-careem-integration-operator-testing.enabled
#  - name: kafka-resources-delete-safety-operator
#    version: "0.0.1"
#    repository: file://charts/kafka-resources-delete-safety-operator
#    condition: kafka-resources-delete-safety-operator.enabled
#  - name: strimzi-drain-cleaner
#    version: "1.1.0"
#    repository: file://charts/strimzi-drain-cleaner
#    condition: strimzi-drain-cleaner.enabled
#  - name: volume-autoscaler
#    version: "1.0.8"
#    repository: file://charts/volume-autoscaler
#    condition: volume-autoscaler.enabled
#  - name: kwatch
#    version: 0.8.5
#    repository: file://charts/kwatch
#    condition: kwatch.enabled