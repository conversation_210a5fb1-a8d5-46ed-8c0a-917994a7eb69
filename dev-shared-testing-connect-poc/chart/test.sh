helm template . \
--name-template kafka-dev-rh-strimzi-staging \
--namespace kafka-strimzi-operator \
-f values.yaml -f values2.yaml -f values.yaml  \
--kube-version 1.26 \
--values /Users/<USER>/workspace/k8s-tenant-kafka/clusters/dev-rh/helm/kafka-strimzi-operator/values.yaml \
--api-versions acme.cert-manager.io/v1 \
--api-versions acme.cert-manager.io/v1/Challenge \
--api-versions acme.cert-manager.io/v1/Order \
--api-versions actions.summerwind.dev/v1alpha1 \
--api-versions actions.summerwind.dev/v1alpha1/HorizontalRunnerAutoscaler \
--api-versions actions.summerwind.dev/v1alpha1/Runner \
--api-versions actions.summerwind.dev/v1alpha1/RunnerDeployment \
--api-versions actions.summerwind.dev/v1alpha1/RunnerReplicaSet \
--api-versions actions.summerwind.dev/v1alpha1/RunnerSet \
--api-versions admissionregistration.k8s.io/v1 \
--api-versions admissionregistration.k8s.io/v1/MutatingWebhookConfiguration \
--api-versions admissionregistration.k8s.io/v1/ValidatingWebhookConfiguration \
--api-versions apiextensions.crossplane.io/v1 \
--api-versions apiextensions.crossplane.io/v1/CompositeResourceDefinition \
--api-versions apiextensions.crossplane.io/v1/Composition \
--api-versions apiextensions.crossplane.io/v1/CompositionRevision \
--api-versions apiextensions.crossplane.io/v1alpha1 \
--api-versions apiextensions.crossplane.io/v1alpha1/CompositionRevision \
--api-versions apiextensions.crossplane.io/v1alpha1/EnvironmentConfig \
--api-versions apiextensions.crossplane.io/v1beta1 \
--api-versions apiextensions.crossplane.io/v1beta1/CompositionRevision \
--api-versions apiextensions.k8s.io/v1 \
--api-versions apiextensions.k8s.io/v1/CustomResourceDefinition \
--api-versions apiregistration.k8s.io/v1 \
--api-versions apiregistration.k8s.io/v1/APIService \
--api-versions apps/v1 \
--api-versions apps/v1/ControllerRevision \
--api-versions apps/v1/DaemonSet \
--api-versions apps/v1/Deployment \
--api-versions apps/v1/ReplicaSet \
--api-versions apps/v1/StatefulSet \
--api-versions argoproj.io/v1alpha1 \
--api-versions argoproj.io/v1alpha1/AnalysisRun \
--api-versions argoproj.io/v1alpha1/AnalysisTemplate \
--api-versions argoproj.io/v1alpha1/ClusterAnalysisTemplate \
--api-versions argoproj.io/v1alpha1/ClusterWorkflowTemplate \
--api-versions argoproj.io/v1alpha1/CronWorkflow \
--api-versions argoproj.io/v1alpha1/Experiment \
--api-versions argoproj.io/v1alpha1/Rollout \
--api-versions argoproj.io/v1alpha1/Workflow \
--api-versions argoproj.io/v1alpha1/WorkflowArtifactGCTask \
--api-versions argoproj.io/v1alpha1/WorkflowEventBinding \
--api-versions argoproj.io/v1alpha1/WorkflowTaskResult \
--api-versions argoproj.io/v1alpha1/WorkflowTaskSet \
--api-versions argoproj.io/v1alpha1/WorkflowTemplate \
--api-versions autoscaling/v1 \
--api-versions autoscaling/v1/HorizontalPodAutoscaler \
--api-versions autoscaling/v2 \
--api-versions autoscaling/v2/HorizontalPodAutoscaler \
--api-versions aws.upbound.io/v1alpha1 \
--api-versions aws.upbound.io/v1alpha1/StoreConfig \
--api-versions aws.upbound.io/v1beta1 \
--api-versions aws.upbound.io/v1beta1/ProviderConfig \
--api-versions aws.upbound.io/v1beta1/ProviderConfigUsage \
--api-versions batch/v1 \
--api-versions batch/v1/CronJob \
--api-versions batch/v1/Job \
--api-versions cert-manager.io/v1 \
--api-versions cert-manager.io/v1/Certificate \
--api-versions cert-manager.io/v1/CertificateRequest \
--api-versions cert-manager.io/v1/ClusterIssuer \
--api-versions cert-manager.io/v1/Issuer \
--api-versions certificates.k8s.io/v1 \
--api-versions certificates.k8s.io/v1/CertificateSigningRequest \
--api-versions chaos-mesh.org/v1alpha1 \
--api-versions chaos-mesh.org/v1alpha1/AWSChaos \
--api-versions chaos-mesh.org/v1alpha1/AzureChaos \
--api-versions chaos-mesh.org/v1alpha1/BlockChaos \
--api-versions chaos-mesh.org/v1alpha1/DNSChaos \
--api-versions chaos-mesh.org/v1alpha1/GCPChaos \
--api-versions chaos-mesh.org/v1alpha1/HTTPChaos \
--api-versions chaos-mesh.org/v1alpha1/IOChaos \
--api-versions chaos-mesh.org/v1alpha1/JVMChaos \
--api-versions chaos-mesh.org/v1alpha1/KernelChaos \
--api-versions chaos-mesh.org/v1alpha1/NetworkChaos \
--api-versions chaos-mesh.org/v1alpha1/PhysicalMachine \
--api-versions chaos-mesh.org/v1alpha1/PhysicalMachineChaos \
--api-versions chaos-mesh.org/v1alpha1/PodChaos \
--api-versions chaos-mesh.org/v1alpha1/PodHttpChaos \
--api-versions chaos-mesh.org/v1alpha1/PodIOChaos \
--api-versions chaos-mesh.org/v1alpha1/PodNetworkChaos \
--api-versions chaos-mesh.org/v1alpha1/RemoteCluster \
--api-versions chaos-mesh.org/v1alpha1/Schedule \
--api-versions chaos-mesh.org/v1alpha1/StatusCheck \
--api-versions chaos-mesh.org/v1alpha1/StressChaos \
--api-versions chaos-mesh.org/v1alpha1/TimeChaos \
--api-versions chaos-mesh.org/v1alpha1/Workflow \
--api-versions chaos-mesh.org/v1alpha1/WorkflowNode \
--api-versions configuration.konghq.com/v1 \
--api-versions configuration.konghq.com/v1/KongClusterPlugin \
--api-versions configuration.konghq.com/v1/KongConsumer \
--api-versions configuration.konghq.com/v1/KongCredential \
--api-versions configuration.konghq.com/v1/KongIngress \
--api-versions configuration.konghq.com/v1/KongPlugin \
--api-versions configuration.konghq.com/v1alpha1 \
--api-versions configuration.konghq.com/v1alpha1/IngressClassParameters \
--api-versions configuration.konghq.com/v1beta1 \
--api-versions configuration.konghq.com/v1beta1/KongConsumerGroup \
--api-versions configuration.konghq.com/v1beta1/TCPIngress \
--api-versions configuration.konghq.com/v1beta1/UDPIngress \
--api-versions coordination.k8s.io/v1 \
--api-versions coordination.k8s.io/v1/Lease \
--api-versions core.strimzi.io/v1beta2 \
--api-versions core.strimzi.io/v1beta2/StrimziPodSet \
--api-versions crd.k8s.amazonaws.com/v1alpha1 \
--api-versions crd.k8s.amazonaws.com/v1alpha1/ENIConfig \
--api-versions discovery.k8s.io/v1 \
--api-versions discovery.k8s.io/v1/EndpointSlice \
--api-versions ec2.aws.upbound.io/v1beta1 \
--api-versions ec2.aws.upbound.io/v1beta1/AMI \
--api-versions ec2.aws.upbound.io/v1beta1/AMICopy \
--api-versions ec2.aws.upbound.io/v1beta1/AMILaunchPermission \
--api-versions ec2.aws.upbound.io/v1beta1/AvailabilityZoneGroup \
--api-versions ec2.aws.upbound.io/v1beta1/CapacityReservation \
--api-versions ec2.aws.upbound.io/v1beta1/CarrierGateway \
--api-versions ec2.aws.upbound.io/v1beta1/CustomerGateway \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultNetworkACL \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultRouteTable \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultSecurityGroup \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultSubnet \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultVPC \
--api-versions ec2.aws.upbound.io/v1beta1/DefaultVPCDHCPOptions \
--api-versions ec2.aws.upbound.io/v1beta1/EBSDefaultKMSKey \
--api-versions ec2.aws.upbound.io/v1beta1/EBSEncryptionByDefault \
--api-versions ec2.aws.upbound.io/v1beta1/EBSSnapshot \
--api-versions ec2.aws.upbound.io/v1beta1/EBSSnapshotCopy \
--api-versions ec2.aws.upbound.io/v1beta1/EBSSnapshotImport \
--api-versions ec2.aws.upbound.io/v1beta1/EBSVolume \
--api-versions ec2.aws.upbound.io/v1beta1/EIP \
--api-versions ec2.aws.upbound.io/v1beta1/EIPAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/EgressOnlyInternetGateway \
--api-versions ec2.aws.upbound.io/v1beta1/FlowLog \
--api-versions ec2.aws.upbound.io/v1beta1/Host \
--api-versions ec2.aws.upbound.io/v1beta1/Instance \
--api-versions ec2.aws.upbound.io/v1beta1/InstanceState \
--api-versions ec2.aws.upbound.io/v1beta1/InternetGateway \
--api-versions ec2.aws.upbound.io/v1beta1/KeyPair \
--api-versions ec2.aws.upbound.io/v1beta1/LaunchTemplate \
--api-versions ec2.aws.upbound.io/v1beta1/MainRouteTableAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/ManagedPrefixList \
--api-versions ec2.aws.upbound.io/v1beta1/ManagedPrefixListEntry \
--api-versions ec2.aws.upbound.io/v1beta1/NATGateway \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkACL \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkACLRule \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkInsightsAnalysis \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkInsightsPath \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkInterface \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkInterfaceAttachment \
--api-versions ec2.aws.upbound.io/v1beta1/NetworkInterfaceSgAttachment \
--api-versions ec2.aws.upbound.io/v1beta1/PlacementGroup \
--api-versions ec2.aws.upbound.io/v1beta1/Route \
--api-versions ec2.aws.upbound.io/v1beta1/RouteTable \
--api-versions ec2.aws.upbound.io/v1beta1/RouteTableAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/SecurityGroup \
--api-versions ec2.aws.upbound.io/v1beta1/SecurityGroupRule \
--api-versions ec2.aws.upbound.io/v1beta1/SerialConsoleAccess \
--api-versions ec2.aws.upbound.io/v1beta1/SnapshotCreateVolumePermission \
--api-versions ec2.aws.upbound.io/v1beta1/SpotDatafeedSubscription \
--api-versions ec2.aws.upbound.io/v1beta1/SpotFleetRequest \
--api-versions ec2.aws.upbound.io/v1beta1/SpotInstanceRequest \
--api-versions ec2.aws.upbound.io/v1beta1/Subnet \
--api-versions ec2.aws.upbound.io/v1beta1/SubnetCidrReservation \
--api-versions ec2.aws.upbound.io/v1beta1/Tag \
--api-versions ec2.aws.upbound.io/v1beta1/TrafficMirrorFilter \
--api-versions ec2.aws.upbound.io/v1beta1/TrafficMirrorFilterRule \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGateway \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayConnect \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayConnectPeer \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayMulticastDomain \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayMulticastDomainAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayMulticastGroupMember \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayMulticastGroupSource \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayPeeringAttachment \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayPeeringAttachmentAccepter \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayPolicyTable \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayPrefixListReference \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayRoute \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayRouteTable \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayRouteTableAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayRouteTablePropagation \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayVPCAttachment \
--api-versions ec2.aws.upbound.io/v1beta1/TransitGatewayVPCAttachmentAccepter \
--api-versions ec2.aws.upbound.io/v1beta1/VPC \
--api-versions ec2.aws.upbound.io/v1beta1/VPCDHCPOptions \
--api-versions ec2.aws.upbound.io/v1beta1/VPCDHCPOptionsAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpoint \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointConnectionNotification \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointRouteTableAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointSecurityGroupAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointService \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointServiceAllowedPrincipal \
--api-versions ec2.aws.upbound.io/v1beta1/VPCEndpointSubnetAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIPv4CidrBlockAssociation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIpam \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIpamPool \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIpamPoolCidr \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIpamPoolCidrAllocation \
--api-versions ec2.aws.upbound.io/v1beta1/VPCIpamScope \
--api-versions ec2.aws.upbound.io/v1beta1/VPCPeeringConnection \
--api-versions ec2.aws.upbound.io/v1beta1/VPCPeeringConnectionAccepter \
--api-versions ec2.aws.upbound.io/v1beta1/VPCPeeringConnectionOptions \
--api-versions ec2.aws.upbound.io/v1beta1/VPNConnection \
--api-versions ec2.aws.upbound.io/v1beta1/VPNConnectionRoute \
--api-versions ec2.aws.upbound.io/v1beta1/VPNGateway \
--api-versions ec2.aws.upbound.io/v1beta1/VPNGatewayAttachment \
--api-versions ec2.aws.upbound.io/v1beta1/VPNGatewayRoutePropagation \
--api-versions ec2.aws.upbound.io/v1beta1/VolumeAttachment \
--api-versions elbv2.aws.upbound.io/v1beta1 \
--api-versions elbv2.aws.upbound.io/v1beta1/LB \
--api-versions elbv2.aws.upbound.io/v1beta1/LBListener \
--api-versions elbv2.aws.upbound.io/v1beta1/LBListenerRule \
--api-versions elbv2.aws.upbound.io/v1beta1/LBTargetGroup \
--api-versions elbv2.aws.upbound.io/v1beta1/LBTargetGroupAttachment \
--api-versions elbv2.k8s.aws/v1alpha1 \
--api-versions elbv2.k8s.aws/v1alpha1/TargetGroupBinding \
--api-versions elbv2.k8s.aws/v1beta1 \
--api-versions elbv2.k8s.aws/v1beta1/IngressClassParams \
--api-versions elbv2.k8s.aws/v1beta1/TargetGroupBinding \
--api-versions events.k8s.io/v1 \
--api-versions events.k8s.io/v1/Event \
--api-versions eventtracker.litmuschaos.io/v1 \
--api-versions eventtracker.litmuschaos.io/v1/EventTrackerPolicy \
--api-versions external-secrets.io/v1alpha1 \
--api-versions external-secrets.io/v1alpha1/ClusterSecretStore \
--api-versions external-secrets.io/v1alpha1/ExternalSecret \
--api-versions external-secrets.io/v1alpha1/PushSecret \
--api-versions external-secrets.io/v1alpha1/SecretStore \
--api-versions external-secrets.io/v1beta1 \
--api-versions external-secrets.io/v1beta1/ClusterExternalSecret \
--api-versions external-secrets.io/v1beta1/ClusterSecretStore \
--api-versions external-secrets.io/v1beta1/ExternalSecret \
--api-versions external-secrets.io/v1beta1/SecretStore \
--api-versions flagger.app/v1beta1 \
--api-versions flagger.app/v1beta1/AlertProvider \
--api-versions flagger.app/v1beta1/Canary \
--api-versions flagger.app/v1beta1/MetricTemplate \
--api-versions flink.apache.org/v1beta1 \
--api-versions flink.apache.org/v1beta1/FlinkDeployment \
--api-versions flink.apache.org/v1beta1/FlinkSessionJob \
--api-versions flowcontrol.apiserver.k8s.io/v1beta2 \
--api-versions flowcontrol.apiserver.k8s.io/v1beta2/FlowSchema \
--api-versions flowcontrol.apiserver.k8s.io/v1beta2/PriorityLevelConfiguration \
--api-versions flowcontrol.apiserver.k8s.io/v1beta3 \
--api-versions flowcontrol.apiserver.k8s.io/v1beta3/FlowSchema \
--api-versions flowcontrol.apiserver.k8s.io/v1beta3/PriorityLevelConfiguration \
--api-versions generators.external-secrets.io/v1alpha1 \
--api-versions generators.external-secrets.io/v1alpha1/ACRAccessToken \
--api-versions generators.external-secrets.io/v1alpha1/ECRAuthorizationToken \
--api-versions generators.external-secrets.io/v1alpha1/Fake \
--api-versions generators.external-secrets.io/v1alpha1/GCRAccessToken \
--api-versions generators.external-secrets.io/v1alpha1/Password \
--api-versions generators.external-secrets.io/v1alpha1/VaultDynamicSecret \
--api-versions helm.toolkit.fluxcd.io/v2beta1 \
--api-versions helm.toolkit.fluxcd.io/v2beta1/HelmRelease \
--api-versions iam.aws.upbound.io/v1beta1 \
--api-versions iam.aws.upbound.io/v1beta1/AccessKey \
--api-versions iam.aws.upbound.io/v1beta1/AccountAlias \
--api-versions iam.aws.upbound.io/v1beta1/AccountPasswordPolicy \
--api-versions iam.aws.upbound.io/v1beta1/Group \
--api-versions iam.aws.upbound.io/v1beta1/GroupMembership \
--api-versions iam.aws.upbound.io/v1beta1/GroupPolicyAttachment \
--api-versions iam.aws.upbound.io/v1beta1/InstanceProfile \
--api-versions iam.aws.upbound.io/v1beta1/OpenIDConnectProvider \
--api-versions iam.aws.upbound.io/v1beta1/Policy \
--api-versions iam.aws.upbound.io/v1beta1/Role \
--api-versions iam.aws.upbound.io/v1beta1/RolePolicy \
--api-versions iam.aws.upbound.io/v1beta1/RolePolicyAttachment \
--api-versions iam.aws.upbound.io/v1beta1/SAMLProvider \
--api-versions iam.aws.upbound.io/v1beta1/ServerCertificate \
--api-versions iam.aws.upbound.io/v1beta1/ServiceLinkedRole \
--api-versions iam.aws.upbound.io/v1beta1/ServiceSpecificCredential \
--api-versions iam.aws.upbound.io/v1beta1/SigningCertificate \
--api-versions iam.aws.upbound.io/v1beta1/User \
--api-versions iam.aws.upbound.io/v1beta1/UserGroupMembership \
--api-versions iam.aws.upbound.io/v1beta1/UserLoginProfile \
--api-versions iam.aws.upbound.io/v1beta1/UserPolicyAttachment \
--api-versions iam.aws.upbound.io/v1beta1/UserSSHKey \
--api-versions iam.aws.upbound.io/v1beta1/VirtualMfaDevice \
--api-versions k6.io/v1alpha1 \
--api-versions k6.io/v1alpha1/K6 \
--api-versions k6.io/v1alpha1/PrivateLoadZone \
--api-versions k6.io/v1alpha1/TestRun \
--api-versions kafka.careem.com/v1beta1 \
--api-versions kafka.careem.com/v1beta1/SchemaRegistrySchema \
--api-versions kafka.strimzi.io/v1alpha1 \
--api-versions kafka.strimzi.io/v1alpha1/KafkaTopic \
--api-versions kafka.strimzi.io/v1alpha1/KafkaUser \
--api-versions kafka.strimzi.io/v1beta1 \
--api-versions kafka.strimzi.io/v1beta1/KafkaTopic \
--api-versions kafka.strimzi.io/v1beta1/KafkaUser \
--api-versions kafka.strimzi.io/v1beta2 \
--api-versions kafka.strimzi.io/v1beta2/Kafka \
--api-versions kafka.strimzi.io/v1beta2/KafkaBridge \
--api-versions kafka.strimzi.io/v1beta2/KafkaConnect \
--api-versions kafka.strimzi.io/v1beta2/KafkaConnector \
--api-versions kafka.strimzi.io/v1beta2/KafkaMirrorMaker \
--api-versions kafka.strimzi.io/v1beta2/KafkaMirrorMaker2 \
--api-versions kafka.strimzi.io/v1beta2/KafkaNodePool \
--api-versions kafka.strimzi.io/v1beta2/KafkaRebalance \
--api-versions kafka.strimzi.io/v1beta2/KafkaTopic \
--api-versions kafka.strimzi.io/v1beta2/KafkaUser \
--api-versions karpenter.k8s.aws/v1alpha1 \
--api-versions karpenter.k8s.aws/v1alpha1/AWSNodeTemplate \
--api-versions karpenter.sh/v1alpha5 \
--api-versions karpenter.sh/v1alpha5/Machine \
--api-versions karpenter.sh/v1alpha5/Provisioner \
--api-versions keda.sh/v1alpha1 \
--api-versions keda.sh/v1alpha1/ClusterTriggerAuthentication \
--api-versions keda.sh/v1alpha1/ScaledJob \
--api-versions keda.sh/v1alpha1/ScaledObject \
--api-versions keda.sh/v1alpha1/TriggerAuthentication \
--api-versions kustomize.toolkit.fluxcd.io/v1 \
--api-versions kustomize.toolkit.fluxcd.io/v1/Kustomization \
--api-versions kustomize.toolkit.fluxcd.io/v1beta1 \
--api-versions kustomize.toolkit.fluxcd.io/v1beta1/Kustomization \
--api-versions kustomize.toolkit.fluxcd.io/v1beta2 \
--api-versions kustomize.toolkit.fluxcd.io/v1beta2/Kustomization \
--api-versions kyverno.io/v1 \
--api-versions kyverno.io/v1/ClusterPolicy \
--api-versions kyverno.io/v1/Policy \
--api-versions kyverno.io/v1alpha2 \
--api-versions kyverno.io/v1alpha2/AdmissionReport \
--api-versions kyverno.io/v1alpha2/BackgroundScanReport \
--api-versions kyverno.io/v1alpha2/ClusterAdmissionReport \
--api-versions kyverno.io/v1alpha2/ClusterBackgroundScanReport \
--api-versions kyverno.io/v1beta1 \
--api-versions kyverno.io/v1beta1/UpdateRequest \
--api-versions kyverno.io/v2alpha1 \
--api-versions kyverno.io/v2alpha1/CleanupPolicy \
--api-versions kyverno.io/v2alpha1/ClusterCleanupPolicy \
--api-versions kyverno.io/v2alpha1/PolicyException \
--api-versions kyverno.io/v2beta1 \
--api-versions kyverno.io/v2beta1/CleanupPolicy \
--api-versions kyverno.io/v2beta1/ClusterCleanupPolicy \
--api-versions kyverno.io/v2beta1/ClusterPolicy \
--api-versions kyverno.io/v2beta1/Policy \
--api-versions kyverno.io/v2beta1/PolicyException \
--api-versions linkerd.io/v1alpha1 \
--api-versions linkerd.io/v1alpha1/ServiceProfile \
--api-versions linkerd.io/v1alpha2 \
--api-versions linkerd.io/v1alpha2/ServiceProfile \
--api-versions litmuschaos.io/v1alpha1 \
--api-versions litmuschaos.io/v1alpha1/ChaosEngine \
--api-versions litmuschaos.io/v1alpha1/ChaosExperiment \
--api-versions litmuschaos.io/v1alpha1/ChaosResult \
--api-versions networking.k8s.aws/v1alpha1 \
--api-versions networking.k8s.aws/v1alpha1/PolicyEndpoint \
--api-versions networking.k8s.io/v1 \
--api-versions networking.k8s.io/v1/Ingress \
--api-versions networking.k8s.io/v1/IngressClass \
--api-versions networking.k8s.io/v1/NetworkPolicy \
--api-versions node.k8s.io/v1 \
--api-versions node.k8s.io/v1/RuntimeClass \
--api-versions notification.toolkit.fluxcd.io/v1 \
--api-versions notification.toolkit.fluxcd.io/v1/Receiver \
--api-versions notification.toolkit.fluxcd.io/v1beta1 \
--api-versions notification.toolkit.fluxcd.io/v1beta1/Alert \
--api-versions notification.toolkit.fluxcd.io/v1beta1/Provider \
--api-versions notification.toolkit.fluxcd.io/v1beta1/Receiver \
--api-versions notification.toolkit.fluxcd.io/v1beta2 \
--api-versions notification.toolkit.fluxcd.io/v1beta2/Alert \
--api-versions notification.toolkit.fluxcd.io/v1beta2/Provider \
--api-versions notification.toolkit.fluxcd.io/v1beta2/Receiver \
--api-versions pingcap.com/v1alpha1 \
--api-versions pingcap.com/v1alpha1/TidbClusterAutoScaler \
--api-versions pingcap.com/v1alpha1/TidbInitializer \
--api-versions pkg.crossplane.io/v1 \
--api-versions pkg.crossplane.io/v1/Configuration \
--api-versions pkg.crossplane.io/v1/ConfigurationRevision \
--api-versions pkg.crossplane.io/v1/Provider \
--api-versions pkg.crossplane.io/v1/ProviderRevision \
--api-versions pkg.crossplane.io/v1alpha1 \
--api-versions pkg.crossplane.io/v1alpha1/ControllerConfig \
--api-versions pkg.crossplane.io/v1beta1 \
--api-versions pkg.crossplane.io/v1beta1/Lock \
--api-versions policy.linkerd.io/v1alpha1 \
--api-versions policy.linkerd.io/v1alpha1/AuthorizationPolicy \
--api-versions policy.linkerd.io/v1alpha1/HTTPRoute \
--api-versions policy.linkerd.io/v1alpha1/MeshTLSAuthentication \
--api-versions policy.linkerd.io/v1alpha1/NetworkAuthentication \
--api-versions policy.linkerd.io/v1alpha1/Server \
--api-versions policy.linkerd.io/v1alpha1/ServerAuthorization \
--api-versions policy.linkerd.io/v1beta1 \
--api-versions policy.linkerd.io/v1beta1/HTTPRoute \
--api-versions policy.linkerd.io/v1beta1/Server \
--api-versions policy.linkerd.io/v1beta1/ServerAuthorization \
--api-versions policy.linkerd.io/v1beta2 \
--api-versions policy.linkerd.io/v1beta2/HTTPRoute \
--api-versions policy/v1 \
--api-versions policy/v1/PodDisruptionBudget \
--api-versions rbac.authorization.k8s.io/v1 \
--api-versions rbac.authorization.k8s.io/v1/ClusterRole \
--api-versions rbac.authorization.k8s.io/v1/ClusterRoleBinding \
--api-versions rbac.authorization.k8s.io/v1/Role \
--api-versions rbac.authorization.k8s.io/v1/RoleBinding \
--api-versions s3.aws.upbound.io/v1beta1 \
--api-versions s3.aws.upbound.io/v1beta1/Bucket \
--api-versions s3.aws.upbound.io/v1beta1/BucketACL \
--api-versions s3.aws.upbound.io/v1beta1/BucketAccelerateConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketAnalyticsConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketCorsConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketIntelligentTieringConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketInventory \
--api-versions s3.aws.upbound.io/v1beta1/BucketLifecycleConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketLogging \
--api-versions s3.aws.upbound.io/v1beta1/BucketMetric \
--api-versions s3.aws.upbound.io/v1beta1/BucketNotification \
--api-versions s3.aws.upbound.io/v1beta1/BucketObject \
--api-versions s3.aws.upbound.io/v1beta1/BucketObjectLockConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketOwnershipControls \
--api-versions s3.aws.upbound.io/v1beta1/BucketPolicy \
--api-versions s3.aws.upbound.io/v1beta1/BucketPublicAccessBlock \
--api-versions s3.aws.upbound.io/v1beta1/BucketReplicationConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketRequestPaymentConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketServerSideEncryptionConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/BucketVersioning \
--api-versions s3.aws.upbound.io/v1beta1/BucketWebsiteConfiguration \
--api-versions s3.aws.upbound.io/v1beta1/Object \
--api-versions s3.aws.upbound.io/v1beta1/ObjectCopy \
--api-versions scheduling.k8s.io/v1 \
--api-versions scheduling.k8s.io/v1/PriorityClass \
--api-versions secrets.crossplane.io/v1alpha1 \
--api-versions secrets.crossplane.io/v1alpha1/StoreConfig \
--api-versions serving.yoda/v1 \
--api-versions serving.yoda/v1/Project \
--api-versions serving.yoda/v1/Serving \
--api-versions skaler.careem.com/v1beta1 \
--api-versions skaler.careem.com/v1beta1/VerticalScalingPolicy \
--api-versions source.toolkit.fluxcd.io/v1 \
--api-versions source.toolkit.fluxcd.io/v1/GitRepository \
--api-versions source.toolkit.fluxcd.io/v1beta1 \
--api-versions source.toolkit.fluxcd.io/v1beta1/Bucket \
--api-versions source.toolkit.fluxcd.io/v1beta1/GitRepository \
--api-versions source.toolkit.fluxcd.io/v1beta1/HelmChart \
--api-versions source.toolkit.fluxcd.io/v1beta1/HelmRepository \
--api-versions source.toolkit.fluxcd.io/v1beta2 \
--api-versions source.toolkit.fluxcd.io/v1beta2/Bucket \
--api-versions source.toolkit.fluxcd.io/v1beta2/GitRepository \
--api-versions source.toolkit.fluxcd.io/v1beta2/HelmChart \
--api-versions source.toolkit.fluxcd.io/v1beta2/HelmRepository \
--api-versions source.toolkit.fluxcd.io/v1beta2/OCIRepository \
--api-versions sparkoperator.k8s.io/v1beta2 \
--api-versions sparkoperator.k8s.io/v1beta2/ScheduledSparkApplication \
--api-versions sparkoperator.k8s.io/v1beta2/SparkApplication \
--api-versions split.smi-spec.io/v1alpha1 \
--api-versions split.smi-spec.io/v1alpha1/TrafficSplit \
--api-versions split.smi-spec.io/v1alpha2 \
--api-versions split.smi-spec.io/v1alpha2/TrafficSplit \
--api-versions storage.k8s.io/v1 \
--api-versions storage.k8s.io/v1/CSIDriver \
--api-versions storage.k8s.io/v1/CSINode \
--api-versions storage.k8s.io/v1/CSIStorageCapacity \
--api-versions storage.k8s.io/v1/StorageClass \
--api-versions storage.k8s.io/v1/VolumeAttachment \
--api-versions storage.k8s.io/v1beta1 \
--api-versions storage.k8s.io/v1beta1/CSIStorageCapacity \
--api-versions v1 \
--api-versions v1/ConfigMap \
--api-versions v1/Endpoints \
--api-versions v1/Event \
--api-versions v1/LimitRange \
--api-versions v1/Namespace \
--api-versions v1/Node \
--api-versions v1/PersistentVolume \
--api-versions v1/PersistentVolumeClaim \
--api-versions v1/Pod \
--api-versions v1/PodTemplate \
--api-versions v1/ReplicationController \
--api-versions v1/ResourceQuota \
--api-versions v1/Secret \
--api-versions v1/Service \
--api-versions v1/ServiceAccount \
--api-versions velero.io/v1 \
--api-versions velero.io/v1/Backup \
--api-versions velero.io/v1/BackupStorageLocation \
--api-versions velero.io/v1/DeleteBackupRequest \
--api-versions velero.io/v1/DownloadRequest \
--api-versions velero.io/v1/PodVolumeBackup \
--api-versions velero.io/v1/PodVolumeRestore \
--api-versions velero.io/v1/ResticRepository \
--api-versions velero.io/v1/Restore \
--api-versions velero.io/v1/Schedule \
--api-versions velero.io/v1/ServerStatusRequest \
--api-versions velero.io/v1/VolumeSnapshotLocation \
--api-versions vpcresources.k8s.aws/v1alpha1 \
--api-versions vpcresources.k8s.aws/v1alpha1/CNINode \
--api-versions vpcresources.k8s.aws/v1beta1 \
--api-versions vpcresources.k8s.aws/v1beta1/SecurityGroupPolicy \
--api-versions wgpolicyk8s.io/v1alpha2 \
--api-versions wgpolicyk8s.io/v1alpha2/ClusterPolicyReport \
--api-versions wgpolicyk8s.io/v1alpha2/PolicyReport \
--include-crds