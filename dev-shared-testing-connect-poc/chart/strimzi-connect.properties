# Bootstrap servers
bootstrap.servers=kafka-dev-shared-testing-kafka-bootstrap:9092
# REST Listeners
rest.port=8083
rest.advertised.host.name=khaled-testing-connect-cluster-connect-tmp.khaled-testing-connect-cluster-connect.kafka-testing.svc
rest.advertised.port=8083
# Plugins
plugin.path=/opt/kafka/plugins
# Provided configuration
offset.storage.topic=connect-qa-replicator-offsets
value.converter=org.apache.kafka.connect.json.JsonConverter
config.storage.topic=connect-qa-replicator-configs
key.converter=org.apache.kafka.connect.json.JsonConverter
group.id=connect-cluster-testing
status.storage.topic=connect-qa-replicator-status
config.storage.replication.factor=-1
offset.storage.replication.factor=-1
status.storage.replication.factor=-1


security.protocol=SASL_PLAINTEXT
producer.security.protocol=SASL_PLAINTEXT
consumer.security.protocol=SASL_PLAINTEXT
admin.security.protocol=SASL_PLAINTEXT


sasl.mechanism=SCRAM-SHA-512
sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="kafka-admin-connect-replicator" password="HcxvZcZAYfkJfs1cVMCLIQn4hjCmrXkg";

producer.sasl.mechanism=SCRAM-SHA-512
producer.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="kafka-admin-connect-replicator" password="HcxvZcZAYfkJfs1cVMCLIQn4hjCmrXkg";

consumer.sasl.mechanism=SCRAM-SHA-512
consumer.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="kafka-admin-connect-replicator" password="HcxvZcZAYfkJfs1cVMCLIQn4hjCmrXkg";

admin.sasl.mechanism=SCRAM-SHA-512
admin.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="kafka-admin-connect-replicator" password="HcxvZcZAYfkJfs1cVMCLIQn4hjCmrXkg";

# Additional configuration
consumer.client.rack=