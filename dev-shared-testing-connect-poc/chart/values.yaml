global:
  serviceName: kafka

kafka:
  clusterName: kafka-dev-shared-testing
  resourcesNamespace: kafka-resources
  clusterNamespace: kafka

kafkaConnect:
  bootstrapServers: kafka-dev-shared-testing-kafka-bootstrap:9092
  authentication:
    username: kafka-connect-testing-poc
    passwordSecretName: kafka-user-kafka-connect-testing-poc
    passwordFieldName: password

debeziumConfigs:
  groupId: kafka-connect-testing-repl-
  topicsPrefix: storage_dbz_

# MySQL Source Database (CDC Source)
mysql:
  enabled: false

# MySQL Destination Database (CDC Target)
mysqlDestination:
  enabled: false

# PostgreSQL Source Database (CDC Source)
postgres:
  enabled: false

# PostgreSQL Destination Database (CDC Target)
postgresDestination:
  enabled: false

# KafkaConnect UI Configuration (Connect-only UI)
kafkaConnectUI:
  enabled: true
  image: landoop/kafka-connect-ui:latest
  port: 8000

# PostGIS Source Database (Spatial CDC Source)
postgis:
  enabled: true

# PostGIS Destination Database (Spatial CDC Target)
postgisDestination:
  enabled: true

# Namespace
namespace: kafka-testing
