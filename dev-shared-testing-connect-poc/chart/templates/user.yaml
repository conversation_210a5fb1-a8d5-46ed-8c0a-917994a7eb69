{{- $configs := include "connect.configs" . | fromYaml }}

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: {{ .Values.kafkaConnect.authentication.username }}
  namespace: {{ .Values.kafka.resourcesNamespace }}
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: {{ .Values.global.serviceName }}
  annotations:
    careem.com/aws-secret-account-name: dev-rh
    careem.com/aws-secret-enable: 'false'
    careem.com/aws-secret-name: storage/qa/governance/dashboard
    careem.com/aws-secret-pass-key-name: kafka_connect_password
    careem.com/aws-secret-user-key-name: kafka_connect_username
    careem.com/force-allow-delete: 'true'
spec:
  authentication:
    type: scram-sha-512
  authorization:
    type: simple
    acls:

      # Allow to list all topics
      - resource:
          type: topic
          name: "*"
          patternType: literal
        operations:
          - Describe

      # Allow to read and write to all connect topics
      - resource:
          type: topic
          name: "{{ .Release.Name }}-"
          patternType: prefix
        operations:
          - Read
          - Write
          - DescribeConfigs

{{- if .Values.mysql.enabled }}
      # Allow to read and write to MySQL CDC topics (prefix pattern)
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
          patternType: prefix
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow to read and write to MySQL CDC heartbeat topic
      - resource:
          type: topic
          name: "__debezium-heartbeat.{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
          patternType: literal
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow to access MySQL CDC consumer groups
      - resource:
          type: group
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
          patternType: prefix
        operations:
          - Read
{{- end }}

{{- if .Values.mysqlDestination.enabled }}
      # Allow Multi-Topic JDBC Sink Connector to read from ALL MySQL CDC topics
      # Using the same prefix pattern as the source connector
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
          patternType: prefix
        operations:
          - Read
          - DescribeConfigs

      # Allow Multi-Topic JDBC Sink Connector consumer group
      - resource:
          type: group
          name: "connect-mysql-multi-table-sink-connector"
          patternType: literal
        operations:
          - Read
{{- end }}

{{- if .Values.postgres.enabled }}
      # Allow PostgreSQL CDC Source Connector to write to topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test"
          patternType: prefix
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow PostgreSQL CDC Source Connector consumer groups
      - resource:
          type: group
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test"
          patternType: prefix
        operations:
          - Read
{{- end }}

{{- if .Values.postgresDestination.enabled }}
      # Allow PostgreSQL Multi-Topic JDBC Sink Connector to read from topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test"
          patternType: prefix
        operations:
          - Read
          - DescribeConfigs

      # Allow PostgreSQL Multi-Topic JDBC Sink Connector consumer group
      - resource:
          type: group
          name: "connect-postgres-multi-table-sink-connector"
          patternType: literal
        operations:
          - Read
{{- end }}

{{- if .Values.postgis.enabled }}
      # Allow PostGIS CDC Source Connector to write to topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test"
          patternType: prefix
        operations:
          - Read
          - Write
          - Create
          - DescribeConfigs

      # Allow PostGIS CDC Source Connector consumer groups
      - resource:
          type: group
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test"
          patternType: prefix
        operations:
          - Read
{{- end }}

{{- if .Values.postgisDestination.enabled }}
      # Allow PostGIS Multi-Topic JDBC Sink Connector to read from topics
      - resource:
          type: topic
          name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test"
          patternType: prefix
        operations:
          - Read
          - DescribeConfigs

      # Allow PostGIS Multi-Topic JDBC Sink Connector consumer group
      - resource:
          type: group
          name: "connect-postgis-multi-table-sink-connector"
          patternType: literal
        operations:
          - Read
{{- end }}

      # Allow to access connect group
      - resource:
          type: group
          name: {{ $configs.groupId }}
          patternType: literal
        operations:
          - Read

      - resource:
          type: group
          name: {{ $configs.groupId }}-
          patternType: prefix
        operations:
          - Read
          - Write
        host: "*"

      # Allow to create dead letter topics
      #      - resource:
      #          type: topic
      #          name: "bdp_failure_"
      #          patternType: prefix
      #        operations:
      #          - Create

{{/*      - resource:*/}}
{{/*          type: topic*/}}
{{/*          name: "*"*/}}
{{/*          patternType: literal*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}

{{/*      - resource:*/}}
{{/*          type: group*/}}
{{/*          name: "connect-storage-dev-rh-"*/}}
{{/*          patternType: prefix*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}
{{/*        host: "*"*/}}
---
{{/*apiVersion: v1*/}}
{{/*kind: Secret*/}}
{{/*metadata:*/}}
{{/*  name: kafka-user-kafka-admin-connect-poc*/}}
{{/*  namespace: kafka-testing*/}}
{{/*data:*/}}
{{/*  password: bkdpMTNzOHI5SEpUUXM1RkE3ZlhYZVRTMmU5REp0YXM=*/}}
{{/*  sasl.jaas.config: >-*/}}
{{/*    b3JnLmFwYWNoZS5rYWZrYS5jb21tb24uc2VjdXJpdHkuc2NyYW0uU2NyYW1Mb2dpbk1vZHVsZSByZXF1aXJlZCB1c2VybmFtZT0ia2Fma2EtYWRtaW4tY29ubmVjdC1wb2MiIHBhc3N3b3JkPSJuR2kxM3M4cjlISlRRczVGQTdmWFhlVFMyZTlESnRhcyI7*/}}
{{/*type: Opaque*/}}
