{{- $configs := include "connect.configs" . | fromYaml }}

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: {{ $configs.clusterName }}
  annotations:
    #  # use-connector-resources configures this KafkaConnect
    #  # to use KafkaConnector resources to avoid
    #  # needing to call the Connect REST API directly
    strimzi.io/use-connector-resources: "true"
spec:
{{/*  image: quay.io/debezium/connect:3.0.7.Final*/}}
  image: docker.io/shqear/debezium:latest
  version: 3.7.0
  replicas: 1
  bootstrapServers: {{ .Values.kafkaConnect.bootstrapServers }}
  authentication:
    type: scram-sha-512
    username: {{ .Values.kafkaConnect.authentication.username }}
    # kubectl get secret kafka-user-kafka-connect-testing-poc --namespace=kafka-resources -o jsonpath='{.data.password}' | \
    #              base64 -d | xargs -I {} kubectl create secret generic kafka-user-kafka-connect-testing-poc --namespace=kafka-testing \
    #               --from-literal=password={}
    passwordSecret:
      secretName: {{ .Values.kafkaConnect.authentication.passwordSecretName }}
      password: {{ .Values.kafkaConnect.authentication.passwordFieldName }}

  #  tls:
  #    trustedCertificates:
  #      - secretName: my-cluster-cluster-ca-cert
  #        pattern: "*.crt"
  config:
    group.id: "{{ $configs.groupId }}"

    # -1 means it will use the default replication factor configured in the broker
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1

    # Topic to use for storing offsets.
    offset.storage.topic: "{{ $configs.internalTopics.offsets }}"

    # Topic to use for storing connector and task configurations
    config.storage.topic: "{{ $configs.internalTopics.configs }}"

    # Topic to use for storing statuses.
    status.storage.topic: "{{ $configs.internalTopics.status }}"