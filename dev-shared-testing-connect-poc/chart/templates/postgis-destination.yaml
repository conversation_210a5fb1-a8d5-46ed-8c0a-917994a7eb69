{{- if .Values.postgisDestination.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgis-destination-config
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-destination
data:
  init.sql: |
    -- Enable PostGIS extension
    CREATE EXTENSION IF NOT EXISTS postgis;
    CREATE EXTENSION IF NOT EXISTS postgis_topology;
    
    -- Create destination spatial tables (structure will be replicated from source)
    CREATE TABLE IF NOT EXISTS locations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        geom GEOMETRY(POINT, 4326),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS routes (
        id SERIAL PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        geom GEOMETRY(LINESTRING, 4326),
        distance_km DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS spatial_events (
        id SERIAL PRIMARY KEY,
        event_type VARCHAR(100) NOT NULL,
        location_id INTEGER REFERENCES locations(id),
        event_area GEOMETRY(POLYGON, 4326),
        event_data JSONB,
        occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

---
apiVersion: v1
kind: Service
metadata:
  name: postgis-destination-service
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-destination
spec:
  selector:
    app: postgis-destination
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgis-destination
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-destination
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgis-destination
  template:
    metadata:
      labels:
        app: postgis-destination
    spec:
      containers:
      - name: postgis
        image: postgis/postgis:15-3.4
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "testdb_postgis_dest"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "destpostgispass123"
        volumeMounts:
        - name: postgis-config
          mountPath: /docker-entrypoint-initdb.d/init.sql
          subPath: init.sql
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgis-config
        configMap:
          name: postgis-destination-config
{{- end }}
