{{- if .Values.kafkaConnectUI.enabled }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-connect-ui
  namespace: {{ .Values.namespace }}
  labels:
    app: kafka-connect-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-connect-ui
  template:
    metadata:
      labels:
        app: kafka-connect-ui
    spec:
      containers:
      - name: kafka-connect-ui
        image: {{ .Values.kafkaConnectUI.image }}
        ports:
        - containerPort: {{ .Values.kafkaConnectUI.port }}
          name: http
        env:
        - name: CONNECT_URL
          value: "http://kafka-connect-release-testing-cluster-connect-api:8083"
        - name: PROXY
          value: "true"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: {{ .Values.kafkaConnectUI.port }}
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: {{ .Values.kafkaConnectUI.port }}
          initialDelaySeconds: 10
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: kafka-connect-ui-service
  namespace: {{ .Values.namespace }}
  labels:
    app: kafka-connect-ui
spec:
  ports:
  - port: {{ .Values.kafkaConnectUI.port }}
    targetPort: {{ .Values.kafkaConnectUI.port }}
    name: http
  selector:
    app: kafka-connect-ui
  type: ClusterIP
{{- end }}
