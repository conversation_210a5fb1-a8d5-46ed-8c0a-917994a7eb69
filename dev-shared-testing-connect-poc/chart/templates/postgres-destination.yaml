{{- if .Values.postgresDestination.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-destination-config
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-destination
data:
  init.sql: |
    -- Create destination database schema
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        user_id INTEGER,
        product_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-destination
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-destination
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-destination
  template:
    metadata:
      labels:
        app: postgres-destination
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "testdb_pg_dest"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "destpostgrespass123"
        volumeMounts:
        - name: postgres-config
          mountPath: /docker-entrypoint-initdb.d/init.sql
          subPath: init.sql
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-destination-config
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-destination-service
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-destination
spec:
  selector:
    app: postgres-destination
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP
{{- end }}
