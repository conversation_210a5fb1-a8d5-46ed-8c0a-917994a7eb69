{{- if .Values.postgres.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-source-config
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-source
data:
  postgresql.conf: |
    # PostgreSQL configuration for CDC
    wal_level = logical
    max_wal_senders = 10
    max_replication_slots = 10
    shared_preload_libraries = 'pgoutput'
    
  init.sql: |
    -- Create a role for table ownership and publication management
    CREATE ROLE table_owners;

    -- Create replication user
    CREATE USER replicator WITH REPLICATION PASSWORD 'replicatorpass123';
    GRANT CONNECT ON DATABASE testdb_pg TO replicator;
    GRANT CREATE ON DATABASE testdb_pg TO replicator;
    GRANT USAGE ON SCHEMA public TO replicator;
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO replicator;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO replicator;

    -- Add both postgres and replicator to the table_owners role
    GRANT table_owners TO postgres;
    GRANT table_owners TO replicator;

    -- Create test tables
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        user_id INTEGER,
        product_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Make the role owner of the tables (after they're created)
    ALTER TABLE users OWNER TO table_owners;
    ALTER TABLE orders OWNER TO table_owners;
    
    -- Insert sample data
    INSERT INTO users (name, email) VALUES 
        ('John Doe PG', '<EMAIL>'),
        ('Jane Smith PG', '<EMAIL>'),
        ('Alice Johnson PG', '<EMAIL>');
    
    INSERT INTO orders (user_id, product_name, quantity, price) VALUES 
        (1, 'PostgreSQL Book', 1, 49.99),
        (2, 'Database Course', 1, 199.99),
        (3, 'SQL Tutorial', 2, 29.99);
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-source
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-source
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-source
  template:
    metadata:
      labels:
        app: postgres-source
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "testdb_pg"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "postgrespass123"
        - name: POSTGRES_INITDB_ARGS
          value: "--wal-segsize=16"
        args:
        - -c
        - wal_level=logical
        - -c
        - max_wal_senders=10
        - -c
        - max_replication_slots=10
        - -c
        - shared_preload_libraries=pgoutput
        volumeMounts:
        - name: postgres-config
          mountPath: /docker-entrypoint-initdb.d/init.sql
          subPath: init.sql
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-source-config
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-source-service
  namespace: {{ .Values.namespace }}
  labels:
    app: postgres-source
spec:
  selector:
    app: postgres-source
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP
{{- end }}
