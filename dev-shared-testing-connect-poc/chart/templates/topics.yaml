{{- $configs := include "connect.configs" . | fromYaml }}
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "{{ $configs.internalTopics.configs }}"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: compact
  partitions: 1
{{/*  replicas: 3*/}}
  replicas: 1
  topicName: "{{ $configs.internalTopics.configs }}"
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "{{ $configs.internalTopics.offsets }}"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: compact
  partitions: 1
  replicas: 1
{{/*  partitions: 25*/}}
{{/*  replicas: 3*/}}
  topicName: "{{ $configs.internalTopics.offsets }}"
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "{{ $configs.internalTopics.status }}"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: compact
  partitions: 1
  replicas: 1
{{/*  partitions: 5*/}}
{{/*  replicas: 3*/}}
  topicName: "{{ $configs.internalTopics.status }}"

{{- if .Values.postgres.enabled }}
---
# PostgreSQL CDC Users Topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-postgres-test-public-users"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test.public.users"
---
# PostgreSQL CDC Orders Topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-postgres-test-public-orders"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test.public.orders"
{{- end }}

{{- if .Values.postgis.enabled }}
---
# PostGIS CDC Locations Topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-postgis-test-public-locations"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test.public.locations"
---
# PostGIS CDC Routes Topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-postgis-test-public-routes"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test.public.routes"
---
# PostGIS CDC Spatial Events Topic
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-postgis-test-public-spatial-events"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test.public.spatial_events"
{{- end }}