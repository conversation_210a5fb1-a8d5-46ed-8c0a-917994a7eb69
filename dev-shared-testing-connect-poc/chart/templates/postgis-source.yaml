{{- if .Values.postgis.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgis-source-config
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-source
data:
  init.sql: |
    -- Enable PostGIS extension
    CREATE EXTENSION IF NOT EXISTS postgis;
    CREATE EXTENSION IF NOT EXISTS postgis_topology;
    
    -- Create a role for table ownership and publication management
    CREATE ROLE spatial_table_owners;
    
    -- Create replication user
    CREATE USER replicator WITH REPLICATION PASSWORD 'replicatorpass123';
    GRANT CONNECT ON DATABASE testdb_postgis TO replicator;
    GRANT CREATE ON DATABASE testdb_postgis TO replicator;
    GRANT USAGE ON SCHEMA public TO replicator;
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO replicator;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO replicator;
    
    -- Add both postgres and replicator to the spatial_table_owners role
    GRANT spatial_table_owners TO postgres;
    GRANT spatial_table_owners TO replicator;
    
    -- Create spatial tables with PostGIS geometry columns
    CREATE TABLE locations (
        id SERIAL PRIMARY KEY,
        name VARCHA<PERSON>(200) NOT NULL,
        description TEXT,
        geom GEOMETRY(POINT, 4326),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE routes (
        id SERIAL PRIMARY KEY,
        name VARCHAR(200) NOT NULL,
        description TEXT,
        geom GEOMETRY(LINESTRING, 4326),
        distance_km DECIMAL(10,2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE spatial_events (
        id SERIAL PRIMARY KEY,
        event_type VARCHAR(100) NOT NULL,
        location_id INTEGER REFERENCES locations(id),
        event_area GEOMETRY(POLYGON, 4326),
        event_data JSONB,
        occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Make the role owner of the tables (after they're created)
    ALTER TABLE locations OWNER TO spatial_table_owners;
    ALTER TABLE routes OWNER TO spatial_table_owners;
    ALTER TABLE spatial_events OWNER TO spatial_table_owners;
    
    -- Insert sample spatial data
    INSERT INTO locations (name, description, geom) VALUES 
    ('Dubai Mall', 'Famous shopping mall in Dubai', ST_GeomFromText('POINT(55.2796 25.1972)', 4326)),
    ('Burj Khalifa', 'Tallest building in the world', ST_GeomFromText('POINT(55.2744 25.1972)', 4326)),
    ('Dubai Marina', 'Waterfront development', ST_GeomFromText('POINT(55.1406 25.0772)', 4326));

    INSERT INTO routes (name, description, geom, distance_km) VALUES 
    ('Dubai Mall to Burj Khalifa', 'Walking route between landmarks', 
     ST_GeomFromText('LINESTRING(55.2796 25.1972, 55.2744 25.1972)', 4326), 0.5),
    ('Marina to Downtown', 'Drive from Marina to Downtown', 
     ST_GeomFromText('LINESTRING(55.1406 25.0772, 55.2744 25.1972)', 4326), 15.2);

    INSERT INTO spatial_events (event_type, location_id, event_area, event_data) VALUES 
    ('Concert', 1, ST_GeomFromText('POLYGON((55.278 25.196, 55.281 25.196, 55.281 25.199, 55.278 25.199, 55.278 25.196))', 4326), 
     '{"artist": "Local Band", "capacity": 5000}'),
    ('Festival', 3, ST_GeomFromText('POLYGON((55.139 25.076, 55.142 25.076, 55.142 25.079, 55.139 25.079, 55.139 25.076))', 4326), 
     '{"type": "Food Festival", "duration_days": 3}');

---
apiVersion: v1
kind: Service
metadata:
  name: postgis-source-service
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-source
spec:
  selector:
    app: postgis-source
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgis-source
  namespace: {{ .Values.namespace }}
  labels:
    app: postgis-source
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgis-source
  template:
    metadata:
      labels:
        app: postgis-source
    spec:
      containers:
      - name: postgis
        image: postgis/postgis:15-3.4
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          value: "testdb_postgis"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "postgispass123"
        - name: POSTGRES_INITDB_ARGS
          value: "--wal-segsize=16"
        args:
        - -c
        - wal_level=logical
        - -c
        - max_wal_senders=10
        - -c
        - max_replication_slots=10
        - -c
        - shared_preload_libraries=pgoutput
        volumeMounts:
        - name: postgis-config
          mountPath: /docker-entrypoint-initdb.d/init.sql
          subPath: init.sql
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgis-config
        configMap:
          name: postgis-source-config
{{- end }}
