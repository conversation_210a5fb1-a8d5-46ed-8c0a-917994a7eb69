{{- $configs := include "connect.configs" . | fromYaml }}

{{- if .Values.mysql.enabled }}
# MySQL CDC Topics
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-mysql-test-testdb-users"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test.testdb.users"
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-mysql-test-testdb-orders"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  partitions: 3
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test.testdb.orders"
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "storage-dbz-mysql-test-schema-history"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 604800000  # 7 days
  partitions: 1
  replicas: 1
  topicName: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test_schema_history"
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  labels:
    strimzi.io/cluster: {{ .Values.kafka.clusterName }}
    service_name: "kafka"
  name: "debezium-heartbeat-storage-dbz-mysql-test"
  namespace: {{ .Values.kafka.resourcesNamespace }}
  annotations:
    careem.com/force-allow-delete: 'true'
spec:
  config:
    cleanup.policy: delete
    retention.ms: 86400000  # 1 day
  partitions: 1
  replicas: 1
  topicName: "__debezium-heartbeat.{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
---
# MySQL Test Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  name: mysql-test-cdc-connector
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
spec:
  class: io.debezium.connector.mysql.MySqlConnector
  tasksMax: 1
  config:
    connector.class: io.debezium.connector.mysql.MySqlConnector
    database.hostname: "mysql-test-service"
    database.port: "3306"
    database.user: "root"
    database.password: "rootpassword123"
    database.server.id: "184055"
    topic.prefix: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test"
    database.include.list: "testdb"
    table.include.list: "testdb.users,testdb.orders"
    snapshot.mode: "initial"
    # Heartbeat configuration
    heartbeat.interval.ms: "10000"
    heartbeat.topics.prefix: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test_heartbeat"
    # Schema history configuration
    schema.history.internal.kafka.bootstrap.servers: "{{ .Values.kafkaConnect.bootstrapServers }}"
    schema.history.internal.kafka.topic: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test_schema_history"
    schema.history.internal.consumer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.consumer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.consumer.sasl.jaas.config: {{ index (lookup "v1" "Secret" .Values.namespace .Values.kafkaConnect.authentication.passwordSecretName).data "sasl.jaas.config" | b64dec | quote }}
    schema.history.internal.producer.security.protocol: SASL_PLAINTEXT
    schema.history.internal.producer.sasl.mechanism: SCRAM-SHA-512
    schema.history.internal.producer.sasl.jaas.config: {{ index (lookup "v1" "Secret" .Values.namespace .Values.kafkaConnect.authentication.passwordSecretName).data "sasl.jaas.config" | b64dec | quote }}
  tasksMax: 1
{{- end }}

{{- if .Values.mysqlDestination.enabled }}
{{- $configs := include "connect.configs" . | fromYaml }}
---
# Single Multi-Topic MySQL JDBC Sink Connector
# This connector handles ALL tables from the MySQL CDC source
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: mysql-multi-table-sink-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.jdbc.JdbcSinkConnector
  config:
    connector.class: io.debezium.connector.jdbc.JdbcSinkConnector
    connection.url: "*******************************************************"
    connection.username: "root"
    connection.password: "destpassword123"

    # Multi-topic configuration - handles all tables from the CDC source
    # Based on working examples: use escaped dots in regex pattern
    topics.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test\\.testdb\\..*"

    # Use ByLogicalTableRouter transform for dynamic table name mapping
    transforms: "route"
    transforms.route.type: "io.debezium.transforms.ByLogicalTableRouter"
    transforms.route.topic.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}mysql_test\\.testdb\\.(.*)"
    transforms.route.topic.replacement: "$1"
    transforms.route.key.enforce.uniqueness: "false"

    # Connector behavior
    insert.mode: "upsert"
    delete.enabled: "true"
    primary.key.mode: "record_key"
    primary.key.fields: "id"
    schema.evolution: "basic"

    # Performance optimizations
    batch.size: "1000"
    use.reduction.buffer: "true"

    # Connection pool settings for better resource utilization
    connection.pool.min_size: "2"
    connection.pool.max_size: "10"
    connection.pool.acquire_increment: "2"

  # Increase tasks for better parallelism across multiple tables
  tasksMax: 2
{{- end }}

{{- if .Values.postgres.enabled }}
{{- $configs := include "connect.configs" . | fromYaml }}
---
# PostgreSQL CDC Source Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: postgres-test-cdc-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.postgresql.PostgresConnector
  config:
    connector.class: io.debezium.connector.postgresql.PostgresConnector
    database.hostname: "postgres-source-service"
    database.port: "5432"
    database.user: "replicator"
    database.password: "replicatorpass123"
    database.dbname: "testdb_pg"
    database.server.name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test"

    # Logical decoding configuration
    plugin.name: "pgoutput"
    slot.name: "debezium_postgres_slot"
    publication.name: "debezium_publication"
    publication.autocreate.mode: "filtered"

    # Table configuration
    schema.include.list: "public"
    table.include.list: "public.users,public.orders"

    # Snapshot configuration
    snapshot.mode: "initial"

    # Topic configuration
    topic.prefix: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test"

    # PostgreSQL-specific configurations
    replica.identity.autoset.values: "public.*:FULL"
    tombstones.on.delete: "false"
  tasksMax: 1
{{- end }}

{{- if .Values.postgresDestination.enabled }}
{{- $configs := include "connect.configs" . | fromYaml }}
---
# Single Multi-Table PostgreSQL JDBC Sink Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: postgres-multi-table-sink-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.jdbc.JdbcSinkConnector
  config:
    connector.class: io.debezium.connector.jdbc.JdbcSinkConnector
    connection.url: "******************************************************************"
    connection.username: "postgres"
    connection.password: "destpostgrespass123"

    # Multi-topic configuration - handles all tables from the PostgreSQL CDC source
    topics.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test\\.public\\..*"

    # Use ByLogicalTableRouter transform for dynamic table name mapping
    transforms: "route"
    transforms.route.type: "io.debezium.transforms.ByLogicalTableRouter"
    transforms.route.topic.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}postgres_test\\.public\\.(.*)"
    transforms.route.topic.replacement: "$1"
    transforms.route.key.enforce.uniqueness: "false"

    # Connector behavior
    insert.mode: "upsert"
    delete.enabled: "true"
    primary.key.mode: "record_key"
    primary.key.fields: "id"
    schema.evolution: "basic"

    # Performance optimizations
    batch.size: "1000"
    use.reduction.buffer: "true"

    # Connection pool settings
    connection.pool.min_size: "2"
    connection.pool.max_size: "10"
    connection.pool.acquire_increment: "2"

  # Multiple tasks for better parallelism
  tasksMax: 2
{{- end }}

{{- if .Values.postgis.enabled }}
{{- $configs := include "connect.configs" . | fromYaml }}
---
# PostGIS CDC Source Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: postgis-test-cdc-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.postgresql.PostgresConnector
  config:
    connector.class: io.debezium.connector.postgresql.PostgresConnector
    database.hostname: "postgis-source-service"
    database.port: "5432"
    database.user: "replicator"
    database.password: "replicatorpass123"
    database.dbname: "testdb_postgis"
    database.server.name: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test"

    # Logical decoding configuration
    plugin.name: "pgoutput"
    slot.name: "debezium_postgis_slot"
    publication.name: "debezium_postgis_publication"
    publication.autocreate.mode: "filtered"

    # Table configuration - including spatial tables
    schema.include.list: "public"
    table.include.list: "public.locations,public.routes,public.spatial_events"

    # Snapshot configuration
    snapshot.mode: "initial"

    # Topic configuration
    topic.prefix: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test"

    # PostGIS-specific configurations
    replica.identity.autoset.values: "public.*:FULL"
    tombstones.on.delete: "false"

    # Handle spatial data types properly
    binary.handling.mode: "base64"

  tasksMax: 1
{{- end }}

{{- if .Values.postgisDestination.enabled }}
{{- $configs := include "connect.configs" . | fromYaml }}
---
# PostGIS Multi-Table JDBC Sink Connector
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnector
metadata:
  labels:
    strimzi.io/cluster: {{ $configs.clusterName }}
  name: postgis-multi-table-sink-connector
  namespace: {{ .Values.namespace }}
spec:
  class: io.debezium.connector.jdbc.JdbcSinkConnector
  config:
    connector.class: io.debezium.connector.jdbc.JdbcSinkConnector
    connection.url: "**********************************************************************"
    connection.username: "postgres"
    connection.password: "destpostgispass123"

    # Multi-topic configuration - handles all tables from the PostGIS CDC source
    topics.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test\\.public\\..*"

    # Use ByLogicalTableRouter transform for dynamic table name mapping
    transforms: "route"
    transforms.route.type: "io.debezium.transforms.ByLogicalTableRouter"
    transforms.route.topic.regex: "{{ .Values.debeziumConfigs.topicsPrefix }}postgis_test\\.public\\.(.*)"
    transforms.route.topic.replacement: "$1"
    transforms.route.key.enforce.uniqueness: "false"

    # Connector behavior
    insert.mode: "upsert"
    delete.enabled: "true"
    primary.key.mode: "record_key"
    primary.key.fields: "id"
    schema.evolution: "basic"

    # Performance optimizations
    batch.size: "1000"
    use.reduction.buffer: "true"

    # Connection pool settings
    connection.pool.min_size: "2"
    connection.pool.max_size: "10"
    connection.pool.acquire_increment: "2"

    # PostGIS-specific settings for spatial data handling
    quote.sql.identifiers: "always"

  # Multiple tasks for better parallelism
  tasksMax: 2
{{- end }}
