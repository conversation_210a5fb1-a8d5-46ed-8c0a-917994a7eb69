{{/*{{- $configs := include "connect.configs" . | fromYaml }}*/}}

{{/*apiVersion: kafka.strimzi.io/v1beta2*/}}
{{/*kind: KafkaUser*/}}
{{/*metadata:*/}}
{{/*  name: kafka-connect-testing-repl-poc*/}}
{{/*  namespace: kafka-resources*/}}
{{/*  labels:*/}}
{{/*    strimzi.io/cluster: kafka-dev-shared-testing*/}}
{{/*    service_name: kafka*/}}
{{/*  annotations:*/}}
{{/*    careem.com/aws-secret-account-name: dev-rh*/}}
{{/*    careem.com/aws-secret-enable: 'false'*/}}
{{/*    careem.com/aws-secret-name: storage/qa/governance/dashboard*/}}
{{/*    careem.com/aws-secret-pass-key-name: kafka_connect_password*/}}
{{/*    careem.com/aws-secret-user-key-name: kafka_connect_username*/}}
{{/*spec:*/}}
{{/*  authentication:*/}}
{{/*    type: scram-sha-512*/}}
{{/*  authorization:*/}}
{{/*    type: simple*/}}
{{/*    acls:*/}}

{{/*      # Allow to list all topics*/}}
{{/*      - resource:*/}}
{{/*          type: topic*/}}
{{/*          name: "*"*/}}
{{/*          patternType: literal*/}}
{{/*        operations:*/}}
{{/*          - Describe*/}}

{{/*      # Allow to read and write to all connect topics*/}}
{{/*      - resource:*/}}
{{/*          type: topic*/}}
{{/*          name: "{{ .Release.Name }}-"*/}}
{{/*          patternType: prefix*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}
{{/*          - DescribeConfigs*/}}

{{/*      # Allow to access connect group*/}}
{{/*      - resource:*/}}
{{/*          type: group*/}}
{{/*          name: {{ $configs.groupId }}*/}}
{{/*          patternType: literal*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}

{{/*      - resource:*/}}
{{/*          type: group*/}}
{{/*          name: {{ $configs.groupId }}-*/}}
{{/*          patternType: prefix*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}
{{/*        host: "*"*/}}

{{/*      # Allow to create dead letter topics*/}}
{{/*      #      - resource:*/}}
{{/*      #          type: topic*/}}
{{/*      #          name: "bdp_failure_"*/}}
{{/*      #          patternType: prefix*/}}
{{/*      #        operations:*/}}
{{/*      #          - Create*/}}

{{/*      - resource:*/}}
{{/*          type: topic*/}}
{{/*          name: "*"*/}}
{{/*          patternType: literal*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}

{{/*      - resource:*/}}
{{/*          type: group*/}}
{{/*          name: "connect-storage-dev-rh-"*/}}
{{/*          patternType: prefix*/}}
{{/*        operations:*/}}
{{/*          - Read*/}}
{{/*          - Write*/}}
{{/*        host: "*"*/}}
{{/*---*/}}
{{/*apiVersion: v1*/}}
{{/*kind: Secret*/}}
{{/*metadata:*/}}
{{/*  name: kafka-user-kafka-admin-connect-poc*/}}
{{/*  namespace: kafka-testing*/}}
{{/*data:*/}}
{{/*  password: bkdpMTNzOHI5SEpUUXM1RkE3ZlhYZVRTMmU5REp0YXM=*/}}
{{/*  sasl.jaas.config: >-*/}}
{{/*    b3JnLmFwYWNoZS5rYWZrYS5jb21tb24uc2VjdXJpdHkuc2NyYW0uU2NyYW1Mb2dpbk1vZHVsZSByZXF1aXJlZCB1c2VybmFtZT0ia2Fma2EtYWRtaW4tY29ubmVjdC1wb2MiIHBhc3N3b3JkPSJuR2kxM3M4cjlISlRRczVGQTdmWFhlVFMyZTlESnRhcyI7*/}}
{{/*type: Opaque*/}}
