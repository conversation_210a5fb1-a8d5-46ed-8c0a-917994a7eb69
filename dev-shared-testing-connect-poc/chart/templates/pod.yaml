apiVersion: v1
kind: Pod
metadata:
  name: postgres-connectivity-debug
  namespace: kafka-testing
  labels:
    app.kubernetes.io/instance: kafka-connect-release-testing-cluster
    app.kubernetes.io/managed-by: strimzi-cluster-operator
    app.kubernetes.io/name: kafka-connect
    app.kubernetes.io/part-of: strimzi-kafka-connect-release-testing-cluster
    strimzi.io/cluster: kafka-connect-release-testing-cluster
    strimzi.io/component-type: kafka-connect
    strimzi.io/kind: KafkaConnect
    strimzi.io/name: kafka-connect-release-testing-cluster-connect-debug
  annotations:
    strimzi.io/auth-hash: '*********'
spec:
  volumes:
    - name: strimzi-tmp
      emptyDir:
        medium: Memory
        sizeLimit: 5Mi
    - name: kafka-metrics-and-logging
      configMap:
        name: kafka-connect-release-testing-cluster-connect-config
        defaultMode: 420
    - name: kafka-user-kafka-connect-testing-poc
      secret:
        secretName: kafka-user-kafka-connect-testing-poc
        defaultMode: 292
    - name: kube-api-access
      projected:
        sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              name: kube-root-ca.crt
              items:
                - key: ca.crt
                  path: ca.crt
          - downwardAPI:
              items:
                - path: namespace
                  fieldRef:
                    apiVersion: v1
                    fieldPath: metadata.namespace
        defaultMode: 420
  containers:
    - name: postgres-debugger
      image: postgres:latest  # Different image for PostgreSQL connectivity testing
      command: [ "sleep", "infinity" ]  # Keeps the pod running for manual tests
      env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: kafka-user-kafka-connect-testing-poc
              key: password
      volumeMounts:
        - name: strimzi-tmp
          mountPath: /tmp
        - name: kafka-metrics-and-logging
          mountPath: /opt/kafka/custom-config/
        - name: kafka-user-kafka-connect-testing-poc
          mountPath: /opt/kafka/connect-password/kafka-user-kafka-connect-testing-poc
        - name: kube-api-access
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
  restartPolicy: Never