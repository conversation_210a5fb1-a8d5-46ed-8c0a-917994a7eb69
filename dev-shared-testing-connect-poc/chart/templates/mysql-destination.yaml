{{- if .Values.mysqlDestination.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-destination-config
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-destination
data:
  mysql.cnf: |
    [mysqld]
    server-id = 223345
    # Basic configuration for destination MySQL
    # No binlog needed for destination
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql-destination
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-destination
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql-destination
  template:
    metadata:
      labels:
        app: mysql-destination
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        ports:
        - containerPort: 3306
          name: mysql
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "destpassword123"
        - name: MYSQL_DATABASE
          value: "testdb_dest"
        volumeMounts:
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: mysql-config
        configMap:
          name: mysql-destination-config
---
apiVersion: v1
kind: Service
metadata:
  name: mysql-destination-service
  namespace: {{ .Values.namespace }}
  labels:
    app: mysql-destination
spec:
  selector:
    app: mysql-destination
  ports:
  - port: 3306
    targetPort: 3306
    name: mysql
  type: ClusterIP
{{- end }}
