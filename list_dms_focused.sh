#!/bin/bash

# Script to list only DMS Replication Tasks, Replication Instances, and Serverless Replication Configs
# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== AWS DMS Focused Resource Discovery ===${NC}"
echo -e "${CYAN}Searching for: Replication Tasks | Replication Instances | Serverless Replication Configs${NC}"
echo

# Get all profiles
PROFILES=$(aws configure list-profiles)

# Function to get focused DMS resources using Resource Explorer API
get_focused_dms_resources() {
    local profile=$1
    
    echo -e "${YELLOW}Using Resource Explorer API...${NC}"
    
    local found_any=false
    local total_tasks=0
    local total_instances=0
    local total_serverless=0
    
    # Search for Replication Tasks
    echo -e "  ${BLUE}🔍 Searching for Replication Tasks...${NC}"
    local tasks=$(aws --profile "$profile" resource-explorer-2 search \
        --query-string "resourcetype:dms:task" \
        --query 'Resources[].Arn' \
        --output text 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$tasks" ] && [ "$tasks" != "None" ]; then
        found_any=true
        local task_count=$(echo "$tasks" | wc -w)
        total_tasks=$task_count
        echo -e "    ${GREEN}✅ Found $task_count Replication Tasks:${NC}"
        
        for arn in $tasks; do
            local region=$(echo $arn | cut -d':' -f4)
            local task_id=$(echo $arn | cut -d':' -f6)
            echo "      📋 $task_id (Region: $region)"
        done
        echo
    else
        echo -e "    ${YELLOW}❌ No Replication Tasks found${NC}"
    fi
    
    # Search for Replication Instances
    echo -e "  ${BLUE}🔍 Searching for Replication Instances...${NC}"
    local instances=$(aws --profile "$profile" resource-explorer-2 search \
        --query-string "resourcetype:dms:replication-instance" \
        --query 'Resources[].Arn' \
        --output text 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$instances" ] && [ "$instances" != "None" ]; then
        found_any=true
        local instance_count=$(echo "$instances" | wc -w)
        total_instances=$instance_count
        echo -e "    ${GREEN}✅ Found $instance_count Replication Instances:${NC}"
        
        for arn in $instances; do
            local region=$(echo $arn | cut -d':' -f4)
            local instance_id=$(echo $arn | cut -d':' -f6)
            echo "      🖥️  $instance_id (Region: $region)"
        done
        echo
    else
        echo -e "    ${YELLOW}❌ No Replication Instances found${NC}"
    fi
    
    # Search for Serverless Replication Configs
    echo -e "  ${BLUE}🔍 Searching for Serverless Replication Configs...${NC}"
    local serverless=$(aws --profile "$profile" resource-explorer-2 search \
        --query-string "resourcetype:dms:replication-config" \
        --query 'Resources[].Arn' \
        --output text 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$serverless" ] && [ "$serverless" != "None" ]; then
        found_any=true
        local serverless_count=$(echo "$serverless" | wc -w)
        total_serverless=$serverless_count
        echo -e "    ${GREEN}✅ Found $serverless_count Serverless Replication Configs:${NC}"
        
        for arn in $serverless; do
            local region=$(echo $arn | cut -d':' -f4)
            local config_id=$(echo $arn | cut -d':' -f6)
            echo "      ⚡ $config_id (Region: $region)"
        done
        echo
    else
        echo -e "    ${YELLOW}❌ No Serverless Replication Configs found${NC}"
    fi
    
    # Summary for this profile
    if [ "$found_any" = true ]; then
        echo -e "  ${CYAN}📊 Profile Summary: $total_tasks tasks | $total_instances instances | $total_serverless serverless${NC}"
    else
        echo -e "  ${YELLOW}📊 No DMS resources found in this profile${NC}"
    fi
    
    return 0
}

# Fallback function using direct DMS API calls
get_focused_dms_fallback() {
    local profile=$1
    
    echo -e "${YELLOW}Fallback: Using direct DMS API calls...${NC}"
    
    # Get default region for region list
    local default_region=$(aws --profile "$profile" configure get region 2>/dev/null || echo "us-east-1")
    
    # Get all regions
    local regions=$(aws --profile "$profile" --region "$default_region" ec2 describe-regions --query 'Regions[].RegionName' --output text 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to get regions list${NC}"
        return 1
    fi
    
    local found_any=false
    local total_tasks=0
    local total_instances=0
    local total_serverless=0
    
    for region in $regions; do
        local region_has_resources=false
        
        # Check replication tasks
        local tasks=$(aws --profile "$profile" --region "$region" dms describe-replication-tasks --query 'ReplicationTasks[].ReplicationTaskIdentifier' --output text 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$tasks" ] && [ "$tasks" != "None" ]; then
            if [ "$region_has_resources" = false ]; then
                echo -e "  ${GREEN}Region: $region${NC}"
                region_has_resources=true
                found_any=true
            fi
            local task_count=$(echo "$tasks" | wc -w)
            total_tasks=$((total_tasks + task_count))
            echo -e "    ${YELLOW}📋 Replication Tasks ($task_count):${NC}"
            for task in $tasks; do
                echo "      - $task"
            done
        fi
        
        # Check replication instances
        local instances=$(aws --profile "$profile" --region "$region" dms describe-replication-instances --query 'ReplicationInstances[].ReplicationInstanceIdentifier' --output text 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$instances" ] && [ "$instances" != "None" ]; then
            if [ "$region_has_resources" = false ]; then
                echo -e "  ${GREEN}Region: $region${NC}"
                region_has_resources=true
                found_any=true
            fi
            local instance_count=$(echo "$instances" | wc -w)
            total_instances=$((total_instances + instance_count))
            echo -e "    ${YELLOW}🖥️  Replication Instances ($instance_count):${NC}"
            for instance in $instances; do
                echo "      - $instance"
            done
        fi
        
        # Check serverless replication configs
        local serverless=$(aws --profile "$profile" --region "$region" dms describe-replication-configs --query 'ReplicationConfigs[].ReplicationConfigIdentifier' --output text 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$serverless" ] && [ "$serverless" != "None" ]; then
            if [ "$region_has_resources" = false ]; then
                echo -e "  ${GREEN}Region: $region${NC}"
                region_has_resources=true
                found_any=true
            fi
            local serverless_count=$(echo "$serverless" | wc -w)
            total_serverless=$((total_serverless + serverless_count))
            echo -e "    ${YELLOW}⚡ Serverless Replication Configs ($serverless_count):${NC}"
            for config in $serverless; do
                echo "      - $config"
            done
        fi
        
        if [ "$region_has_resources" = true ]; then
            echo
        fi
    done
    
    # Summary for this profile
    if [ "$found_any" = true ]; then
        echo -e "  ${CYAN}📊 Profile Summary: $total_tasks tasks | $total_instances instances | $total_serverless serverless${NC}"
    else
        echo -e "  ${YELLOW}📊 No DMS resources found in this profile${NC}"
    fi
    
    return 0
}

# Main scanning loop
total_profiles=0
scanned_profiles=0
error_profiles=0
grand_total_tasks=0
grand_total_instances=0
grand_total_serverless=0

for profile in $PROFILES; do
    total_profiles=$((total_profiles + 1))
    echo -e "${BLUE}=== 🔍 Scanning profile: $profile ===${NC}"
    
    # Test if profile is accessible
    aws --profile "$profile" sts get-caller-identity >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Profile $profile is not accessible (authentication required)${NC}"
        error_profiles=$((error_profiles + 1))
        echo
        continue
    fi
    
    scanned_profiles=$((scanned_profiles + 1))
    
    # Try Resource Explorer first, fallback to direct API calls
    get_focused_dms_resources "$profile" || get_focused_dms_fallback "$profile"
    
    echo -e "${GREEN}  ✅ Completed scanning profile: $profile${NC}"
    echo
done

echo -e "${BLUE}=== 📊 Final Summary ===${NC}"
echo -e "Total profiles: $total_profiles"
echo -e "Successfully scanned: $scanned_profiles"
echo -e "Authentication errors: $error_profiles"
echo
echo -e "${GREEN}🎯 Scan completed! Focus: Tasks | Instances | Serverless Replication${NC}"
