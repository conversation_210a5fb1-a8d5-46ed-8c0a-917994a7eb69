#!/bin/bash

# Script to list all AWS DMS resources using AWS Resource Explorer API
# This is the most efficient method as it searches across all regions in one call
# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== AWS DMS Resources Discovery (Using Resource Explorer API) ===${NC}"
echo "Starting comprehensive scan across all profiles using AWS Resource Explorer..."
echo

# Get all profiles
PROFILES=$(aws configure list-profiles)

# Function to get DMS resources using Resource Explorer API
get_dms_resources_explorer() {
    local profile=$1
    
    echo -e "${YELLOW}Searching for DMS resources using Resource Explorer...${NC}"
    
    # Search for all DMS resource types using Resource Explorer
    local search_queries=(
        "resourcetype:dms:replication-instance"
        "resourcetype:dms:task" 
        "resourcetype:dms:endpoint"
        "resourcetype:dms:replication-subnet-group"
        "resourcetype:dms:certificate"
    )
    
    local found_any=false
    
    for query in "${search_queries[@]}"; do
        local resource_type=$(echo $query | cut -d':' -f2-)
        echo -e "  ${BLUE}Searching for $resource_type...${NC}"
        
        local resources=$(aws --profile "$profile" resource-explorer-2 search \
            --query-string "$query" \
            --query 'Resources[].Arn' \
            --output text 2>/dev/null)
        
        if [ $? -eq 0 ] && [ -n "$resources" ] && [ "$resources" != "None" ]; then
            found_any=true
            local count=$(echo "$resources" | wc -w)
            echo -e "    ${GREEN}Found $count resources:${NC}"
            
            for arn in $resources; do
                local region=$(echo $arn | cut -d':' -f4)
                local resource_id=$(echo $arn | cut -d':' -f6)
                echo "      - $resource_id (Region: $region)"
            done
            echo
        else
            echo -e "    ${YELLOW}No $resource_type found${NC}"
        fi
    done
    
    return $found_any
}

# Fallback function using Resource Groups Tagging API if Resource Explorer is not available
get_dms_resources_fallback() {
    local profile=$1
    
    echo -e "${YELLOW}Resource Explorer not available, falling back to Resource Groups API...${NC}"
    
    # Get authenticated profile's default region for region list
    local default_region=$(aws --profile "$profile" configure get region 2>/dev/null || echo "us-east-1")
    
    # Get all regions
    local regions=$(aws --profile "$profile" --region "$default_region" ec2 describe-regions --query 'Regions[].RegionName' --output text 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to get regions list${NC}"
        return 1
    fi
    
    local found_any=false
    
    for region in $regions; do
        local dms_resources=$(aws --profile "$profile" --region "$region" resourcegroupstaggingapi get-resources \
            --resource-type-filters "dms:replication-instance" "dms:task" "dms:endpoint" "dms:replication-subnet-group" "dms:certificate" \
            --query 'ResourceTagMappingList[].ResourceARN' --output text 2>/dev/null)
        
        if [ $? -eq 0 ] && [ -n "$dms_resources" ] && [ "$dms_resources" != "None" ]; then
            if [ "$found_any" = false ]; then
                echo -e "${GREEN}DMS Resources found:${NC}"
                found_any=true
            fi
            
            echo -e "  ${YELLOW}Region: $region${NC}"
            
            for arn in $dms_resources; do
                local resource_type=""
                local resource_id=$(echo $arn | cut -d':' -f6)
                
                if [[ $arn == *":replication-instance:"* ]]; then
                    resource_type="Replication Instance"
                elif [[ $arn == *":task:"* ]]; then
                    resource_type="Replication Task"
                elif [[ $arn == *":endpoint:"* ]]; then
                    resource_type="Endpoint"
                elif [[ $arn == *":subgrp:"* ]]; then
                    resource_type="Subnet Group"
                elif [[ $arn == *":cert:"* ]]; then
                    resource_type="Certificate"
                fi
                
                echo "    - $resource_type: $resource_id"
            done
            echo
        fi
    done
    
    return $found_any
}

# Main scanning loop
total_profiles=0
scanned_profiles=0
error_profiles=0

for profile in $PROFILES; do
    total_profiles=$((total_profiles + 1))
    echo -e "${BLUE}=== Scanning profile: $profile ===${NC}"
    
    # Test if profile is accessible
    aws --profile "$profile" sts get-caller-identity >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Profile $profile is not accessible (authentication required)${NC}"
        error_profiles=$((error_profiles + 1))
        echo
        continue
    fi
    
    scanned_profiles=$((scanned_profiles + 1))
    
    # Try Resource Explorer first, fallback to Resource Groups API
    if ! get_dms_resources_explorer "$profile"; then
        get_dms_resources_fallback "$profile"
    fi
    
    echo -e "${GREEN}  ✅ Completed scanning profile: $profile${NC}"
    echo
done

echo -e "${BLUE}=== Scan Summary ===${NC}"
echo -e "Total profiles: $total_profiles"
echo -e "Successfully scanned: $scanned_profiles"
echo -e "Authentication errors: $error_profiles"
echo
echo -e "${GREEN}Scan completed!${NC}"
