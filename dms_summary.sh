#!/bin/bash

# Quick DMS Summary - Only Tasks, Instances, and Serverless Replication
# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== DMS Resources Summary ===${NC}"
echo -e "${CYAN}Focus: Replication Tasks | Replication Instances | Serverless Replication${NC}"
echo

# Get all profiles
PROFILES=$(aws configure list-profiles)

# Quick check function for authenticated profiles only
check_profile_dms() {
    local profile=$1
    
    # Test if profile is accessible
    aws --profile "$profile" sts get-caller-identity >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    local found_resources=false
    local total_tasks=0
    local total_instances=0
    local total_serverless=0
    
    # Check eu-west-1 (primary region based on previous results)
    local region="eu-west-1"
    
    # Check replication tasks
    local tasks=$(aws --profile "$profile" --region "$region" dms describe-replication-tasks --query 'ReplicationTasks[].ReplicationTaskIdentifier' --output text 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$tasks" ] && [ "$tasks" != "None" ]; then
        if [ "$found_resources" = false ]; then
            echo -e "${GREEN}📋 Profile: $profile${NC}"
            found_resources=true
        fi
        local task_count=$(echo "$tasks" | wc -w)
        total_tasks=$task_count
        echo -e "  ${YELLOW}Replication Tasks ($task_count):${NC}"
        for task in $tasks; do
            echo "    - $task"
        done
    fi
    
    # Check replication instances
    local instances=$(aws --profile "$profile" --region "$region" dms describe-replication-instances --query 'ReplicationInstances[].ReplicationInstanceIdentifier' --output text 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$instances" ] && [ "$instances" != "None" ]; then
        if [ "$found_resources" = false ]; then
            echo -e "${GREEN}📋 Profile: $profile${NC}"
            found_resources=true
        fi
        local instance_count=$(echo "$instances" | wc -w)
        total_instances=$instance_count
        echo -e "  ${YELLOW}Replication Instances ($instance_count):${NC}"
        for instance in $instances; do
            echo "    - $instance"
        done
    fi
    
    # Check serverless replication configs
    local serverless=$(aws --profile "$profile" --region "$region" dms describe-replication-configs --query 'ReplicationConfigs[].ReplicationConfigIdentifier' --output text 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$serverless" ] && [ "$serverless" != "None" ]; then
        if [ "$found_resources" = false ]; then
            echo -e "${GREEN}📋 Profile: $profile${NC}"
            found_resources=true
        fi
        local serverless_count=$(echo "$serverless" | wc -w)
        total_serverless=$serverless_count
        echo -e "  ${YELLOW}Serverless Replication Configs ($serverless_count):${NC}"
        for config in $serverless; do
            echo "    - $config"
        done
    fi
    
    if [ "$found_resources" = true ]; then
        echo -e "  ${CYAN}Summary: $total_tasks tasks | $total_instances instances | $total_serverless serverless${NC}"
        echo
        return 0
    fi
    
    return 1
}

# Main scan
echo -e "${BLUE}Scanning authenticated profiles in eu-west-1...${NC}"
echo

profiles_with_resources=0
total_profiles_scanned=0

for profile in $PROFILES; do
    if check_profile_dms "$profile"; then
        profiles_with_resources=$((profiles_with_resources + 1))
    fi
    total_profiles_scanned=$((total_profiles_scanned + 1))
done

echo -e "${BLUE}=== Final Summary ===${NC}"
echo -e "Profiles scanned: $total_profiles_scanned"
echo -e "Profiles with DMS resources: $profiles_with_resources"
echo -e "${GREEN}✅ Quick scan completed!${NC}"
