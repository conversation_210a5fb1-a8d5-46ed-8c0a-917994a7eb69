#!/bin/bash

# Script to list all AWS DMS resources across all profiles and regions using Resource Groups Tagging API
# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== AWS DMS Resources Discovery (Using Resource Groups API) ===${NC}"
echo "Starting comprehensive scan across all profiles and regions using Resource Groups Tagging API..."
echo

# Get all profiles
PROFILES=$(aws configure list-profiles)

# Get all regions (find first authenticated profile)
AUTHENTICATED_PROFILE=""
for profile in $PROFILES; do
    aws --profile "$profile" sts get-caller-identity >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        AUTHENTICATED_PROFILE="$profile"
        break
    fi
done

if [ -z "$AUTHENTICATED_PROFILE" ]; then
    echo -e "${RED}No authenticated profiles found. Please run 'aws sso login --profile <profile-name>' first.${NC}"
    exit 1
fi

echo -e "${YELLOW}Getting list of AWS regions using authenticated profile: $AUTHENTICATED_PROFILE${NC}"

REGIONS=$(aws --profile "$AUTHENTICATED_PROFILE" --region us-east-1 ec2 describe-regions --query 'Regions[].RegionName' --output text 2>/dev/null)

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to get regions. Please ensure you're authenticated with AWS SSO.${NC}"
    exit 1
fi

echo -e "${GREEN}Found $(echo $REGIONS | wc -w) regions${NC}"
echo -e "${GREEN}Found $(echo "$PROFILES" | wc -l) profiles${NC}"
echo

# Function to get DMS resources using Resource Groups Tagging API
get_dms_resources_by_tags() {
    local profile=$1
    local region=$2

    # Get all DMS resources using resource groups tagging API
    local dms_resources=$(aws --profile "$profile" --region "$region" resourcegroupstaggingapi get-resources \
        --resource-type-filters "dms:replication-instance" "dms:task" "dms:endpoint" "dms:replication-subnet-group" "dms:certificate" \
        --query 'ResourceTagMappingList[].ResourceARN' --output text 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$dms_resources" ] && [ "$dms_resources" != "None" ]; then
        echo -e "${GREEN}Profile: $profile | Region: $region${NC}"

        # Parse and categorize resources
        local instances=()
        local tasks=()
        local endpoints=()
        local subnet_groups=()
        local certificates=()

        for arn in $dms_resources; do
            if [[ $arn == *":replication-instance:"* ]]; then
                instances+=($(echo $arn | cut -d':' -f6))
            elif [[ $arn == *":task:"* ]]; then
                tasks+=($(echo $arn | cut -d':' -f6))
            elif [[ $arn == *":endpoint:"* ]]; then
                endpoints+=($(echo $arn | cut -d':' -f6))
            elif [[ $arn == *":subgrp:"* ]]; then
                subnet_groups+=($(echo $arn | cut -d':' -f6))
            elif [[ $arn == *":cert:"* ]]; then
                certificates+=($(echo $arn | cut -d':' -f6))
            fi
        done

        # Display categorized resources
        if [ ${#instances[@]} -gt 0 ]; then
            echo -e "  ${YELLOW}Replication Instances (${#instances[@]}):${NC}"
            for instance in "${instances[@]}"; do
                echo "    - $instance"
            done
        fi

        if [ ${#tasks[@]} -gt 0 ]; then
            echo -e "  ${YELLOW}Replication Tasks (${#tasks[@]}):${NC}"
            for task in "${tasks[@]}"; do
                echo "    - $task"
            done
        fi

        if [ ${#endpoints[@]} -gt 0 ]; then
            echo -e "  ${YELLOW}Endpoints (${#endpoints[@]}):${NC}"
            for endpoint in "${endpoints[@]}"; do
                echo "    - $endpoint"
            done
        fi

        if [ ${#subnet_groups[@]} -gt 0 ]; then
            echo -e "  ${YELLOW}Subnet Groups (${#subnet_groups[@]}):${NC}"
            for group in "${subnet_groups[@]}"; do
                echo "    - $group"
            done
        fi

        if [ ${#certificates[@]} -gt 0 ]; then
            echo -e "  ${YELLOW}Certificates (${#certificates[@]}):${NC}"
            for cert in "${certificates[@]}"; do
                echo "    - $cert"
            done
        fi

        echo
        return 0
    fi

    return 1
}

# Main scanning loop using Resource Groups API
total_profiles=0
scanned_profiles=0
error_profiles=0
total_resources_found=0

for profile in $PROFILES; do
    total_profiles=$((total_profiles + 1))
    echo -e "${BLUE}Scanning profile: $profile${NC}"

    # Test if profile is accessible
    aws --profile "$profile" sts get-caller-identity >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo -e "${RED}  ❌ Profile $profile is not accessible (authentication required)${NC}"
        error_profiles=$((error_profiles + 1))
        echo
        continue
    fi

    scanned_profiles=$((scanned_profiles + 1))
    profile_resources_found=0

    # Use Resource Groups API to find DMS resources across all regions
    echo -e "  ${YELLOW}Using Resource Groups API to scan all regions...${NC}"

    for region in $REGIONS; do
        if get_dms_resources_by_tags "$profile" "$region"; then
            profile_resources_found=1
        fi
    done

    if [ $profile_resources_found -eq 0 ]; then
        echo -e "  ${YELLOW}No DMS resources found in any region${NC}"
    fi

    echo -e "${GREEN}  ✅ Completed scanning profile: $profile${NC}"
    echo
done

echo -e "${BLUE}=== Scan Summary ===${NC}"
echo -e "Total profiles: $total_profiles"
echo -e "Successfully scanned: $scanned_profiles"
echo -e "Authentication errors: $error_profiles"
echo
echo -e "${GREEN}Scan completed!${NC}"
